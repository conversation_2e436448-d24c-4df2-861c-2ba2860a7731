package com.birdeye.social.service.SocialEngageService;

import com.birdeye.social.businessCore.IBusinessCoreService;
import com.birdeye.social.cache.CacheManager;
import com.birdeye.social.cache.SystemPropertiesCache;
import com.birdeye.social.constant.*;
import com.birdeye.social.constant.EngageV2FeedTypeEnum;
import com.birdeye.social.constant.FBNotificationKafkaEventNameEnum;
import com.birdeye.social.constant.SocialChannel;
import com.birdeye.social.dao.*;
import com.birdeye.social.dto.*;
import com.birdeye.social.entities.*;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.exception.SocialException;
import com.birdeye.social.external.request.media.PicturesqueMediaUploadRequest;
import com.birdeye.social.external.service.KafkaProducerService;
import com.birdeye.social.external.service.PicturesqueGen;
import com.birdeye.social.facebook.FacebookService;
import com.birdeye.social.facebook.FbMessageTags;
import com.birdeye.social.insights.PostData;
import com.birdeye.social.insights.constants.ElasticConstants;
import com.birdeye.social.linkedin.LinkedinService;
import com.birdeye.social.model.*;
import com.birdeye.social.model.FbNotification.EngageNotificationDetails;
import com.birdeye.social.model.FbNotification.FreshPostNotificationRequest;
import com.birdeye.social.model.engageV2.*;
import com.birdeye.social.model.engageV2.message.EventUpdateRequest;
import com.birdeye.social.model.engageV2.message.ExternalServiceEvent;
import com.birdeye.social.model.engageV2.message.InboxMessageRequest;
import com.birdeye.social.model.engageV2.message.MessageAttachment;
import com.birdeye.social.model.linkinbio.BusinessPropertyEventRequest;
import com.birdeye.social.nexus.NexusService;
import com.birdeye.social.nlp.NLPService;
import com.birdeye.social.platform.dao.BusinessFacebookPageRepository;
import com.birdeye.social.platform.dao.BusinessRepository;
import com.birdeye.social.service.*;
import com.birdeye.social.service.SocialEngageService.converter.EngageConverterService;
import com.birdeye.social.service.Youtube.YoutubeService;
import com.birdeye.social.service.instagram.impl.IInstagramService;
import com.birdeye.social.service.tiktok.TiktokAccountService;
import com.birdeye.social.sro.GoogleAuthToken;
import com.birdeye.social.sro.SocialTagBasicDetail;
import com.birdeye.social.sro.SocialTagEntityMappingActionEvent;
import com.birdeye.social.utils.*;
import com.birdeye.social.youtube.*;
import com.fasterxml.jackson.annotation.JsonInclude;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.elasticsearch.action.get.GetResponse;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.client.core.CountRequest;
import org.elasticsearch.client.core.CountResponse;
import org.elasticsearch.common.xcontent.XContentType;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.functionscore.ScriptScoreFunctionBuilder;
import org.elasticsearch.index.reindex.BulkByScrollResponse;
import org.elasticsearch.index.reindex.UpdateByQueryRequest;
import org.elasticsearch.script.Script;
import org.elasticsearch.script.ScriptType;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.aggregations.bucket.terms.ParsedTerms;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.aggregations.metrics.ParsedTopHits;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.ws.rs.BadRequestException;
import java.io.IOException;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.birdeye.social.constant.EngageV2FeedTypeEnum.AD_COMMENT;
import static com.birdeye.social.constant.EngageV2FeedTypeEnum.COMMENT;
import static com.birdeye.social.constant.FBNotificationKafkaEventNameEnum.SOCIAL_ENGAGE_FEED_ES_TOPIC;
import static com.birdeye.social.constant.FBNotificationKafkaEventNameEnum.SOCIAL_ENGAGE_FEED_ES_TOPIC_UPDATE;

@Service
public class SocialEngagementV2ServiceImpl implements SocialEngagementV2Service {

    @Autowired
    private SocialTwitterAccountRepository socialTwitterAccountRepository;

    @Autowired
    private BusinessYoutubeChannelRepository youtubeChannelRepository;

    @Autowired
    private BusinessTiktokAccountsRepository businessTiktokAccountsRepository;

    @Autowired
    private TiktokAccountService tiktokAccountService;

    @Autowired
    private SocialPostFacebookService socialPostFacebookService;

    @Autowired
    private IInstagramService instagramService;

    @Autowired
    private BusinessFacebookPageRepository facebookPageRepo;

    @Autowired
    private SocialFBPageRepository socialFBPageRepository;

    @Autowired
    private SocialPostLinkedinService socialPostLinkedinService;

    @Autowired
    private BusinessLinkedinPageRepository linkedinRepo;

    @Autowired
    private BusinessInstagramAccountRepository instagramAccountRepository;

    @Autowired
    private BusinessRepository businessRepository;

    @Autowired
    private IBusinessCoreService businessCoreService;

    @Autowired
    private IFbNotificationService fbNotificationService;

    @Autowired
    private FacebookService facebookService;

    @Autowired
    private EngageFeedDetailsRepo engageFeedDetailsRepo;


    @Autowired
    private EngageFactory engageFactory;

    @Autowired
    private SocialTagService socialTagService;

    @Autowired
    private EngageConverterService engageConverterService;

    @Autowired
    private KafkaProducerService kafkaProducerService;

    @Autowired
    private EngageActionAuditRepo engageActionAuditRepo;
    @Autowired
    private FacebookSocialService facebookSocialService;

    @Autowired
    private EsService esService;

    @Autowired
    private BusinessYoutubeChannelRepository businessYoutubeChannelRepository;
    @Autowired
    private GoogleAuthenticationService googleAuthenticationService;
    @Autowired
    private CommonService commonService;
    @Autowired
    private YoutubeService youtubeService;
    @Autowired
    BusinessInstagramAccountRepository businessInstagramAccountRepository;
    @Autowired
    InstagramSocialService instagramSocialService;

    @Autowired
    private EngageUserDetailsRepo engageUserDetailsRepo;
    @Autowired
    private SocialLinkedinService socialLinkedinService;
    @Autowired
    private NexusService nexusService;

    @Autowired
    private PicturesqueGen picturesqueService;

    @Autowired
    private TwitterSocialAccountService twitterSocialAccountService;

    @Autowired
    private YouTubeAccountService youTubeAccountService;

    @Autowired
    YoutubeEngageLimitRepo youtubeEngageLimitRepo;
    @Autowired
    NLPService nlpService;

    @Autowired
    MediaAssetRepoService mediaAssetRepoService;

    @Autowired
    EngageAlertAuditService engageAlertAuditService;

    @Autowired
    private LinkedinService linkedinService;

    @Autowired
    public CacheService cacheService;

    @Autowired
    private SamayService samayService;

    private static final Logger LOGGER = LoggerFactory.getLogger(SocialEngagementV2ServiceImpl.class);
    private static final String LINKEDIN_URN_PREFIX="urn:li:organization:";

    private static final String ENGAGE = "ENGAGE";
    private static final String VALUE = "value";
    private static final String RAW_FEED_ID = "rawFeedId";
    private static final String PAINLESS  = "painless";
    private static final String SOURCE_DELETE  = "ctx._source.isDeleted = params.value";
    private static final String INVALID_REQUEST = "Invalid Request";
    private static final String PARENT_COMMENT_NOT_FOUND = "No parent comment found";
    private static final String FEED_DATE = "feedDate";
    private static final String IS_PARENT_COMMENT_ES = "isParentComment";
    private static final String POST_ID_ES = "postId";
    private static final String EVENT_PARENT_ID_ES = "eventParentId";
    private static final String PAGE_ID_ES = "pageId";
    private static final String IS_DELETED_ES = "isDeleted";
    private static final String INTERNAL_ERROR_MSG = "Something went wrong";
    private static final String INTERNAL_DATA_CONVERSION_ERROR_MSG = "Could not convert post data to social time due to error ";
    private static final String QUERY_DOC_LOG_MSG = "Query documents: {} ";
    private static final String UPDATED_DOC_LOG_MSG = "Updated documents: {} ";
    private static final String NO_DOC_FOUND_LOG_MSG ="No doc found in ES to be updated for feedId {}";
    private static final String NO_COMMENT_DOC_FOUND_LOG_MSG = "No doc found in ES to be updated for commentId {}";
    private static final String ES_FETCH_ERROR_LOG_MSG = "Something went wrong while fetching data from ES with an error ";
    private static final String NO_PARENT_COMMENT_DOC_FOUND_LOG_MSG = "No parent comment found for postId {}";
    private static final String ES_INTERNAL_ERROR_LOG_MSG = "Something went wrong while fetching data from es for commentId {} with error {}";
    private static final String FB_URL = "https://facebook.com/";
    private static final String KEYWORD = ".keyword";
    public static final String PAGE_ID = "pageId.keyword";

    private static final String INSTAGRAM_POST_DELETE_ERROR_MSG = "This operation is not supported for Instagram post";

    private Random secureRandom = SecureRandom.getInstanceStrong();

    public SocialEngagementV2ServiceImpl() throws NoSuchAlgorithmException {
    }

    @Override
    public void hideWallPost(SocialEngageObjectRequest request) {
        String action = null;
        try {
            SocialEngageV2 execute = engageFactory.getSocialEngageChannel(request.getChannel())
                    .orElseThrow(() -> new IllegalArgumentException(INVALID_REQUEST));

            execute.hideWallPost(request);

            // update es doc.

            action = Objects.nonNull(request.getHideObject()) && request.getHideObject() ?
                    EngageActionsEnum.HIDE_POST.name() :  EngageActionsEnum.UNHIDE_POST.name();
            auditEngageActions(action, request, Constants.SUCCESS, null);
        } catch (Exception ex) {
            auditEngageActions(action, request, Constants.FAILED, ex.getMessage());
            throw ex;
        }
    }

    @Override
    public void hidePostComment(SocialEngageObjectRequest request) throws IOException {
        LOGGER.info("Request received to hide comment for payload: {}",request);
        String action = null;
        try {
            SocialEngageV2 execute = engageFactory.getSocialEngageChannel(request.getChannel())
                    .orElseThrow(() -> new IllegalArgumentException(INVALID_REQUEST));

            Boolean hide = request.getHide();
            EngageNotificationDetails documentFromEs = getFeedDocumentFromEs(request.getRawFeedId());

            if (Objects.isNull(documentFromEs)) {
                LOGGER.info(NO_DOC_FOUND_LOG_MSG, request.getRawFeedId());
                return;
            }
            request.setFeedId(documentFromEs.getFeedId());
            request.setEngageFeedId(documentFromEs.getEngageFeedId());
            if (EngageV2FeedTypeEnum.POST.name().equalsIgnoreCase(documentFromEs.getType()) && hide) {
                execute.hideWallPost(request);
                action = EngageActionsEnum.HIDE_POST.name();
            } else if (EngageV2FeedTypeEnum.COMMENT.name().equalsIgnoreCase(documentFromEs.getType())||
                    AD_COMMENT.name().equalsIgnoreCase(documentFromEs.getType())) {
                execute.hidePostComment(request);
                action = Objects.nonNull(hide) && hide ? EngageActionsEnum.HIDE_COMMENT.name() : EngageActionsEnum.UNHIDE_COMMENT.name();
            }

            // update es doc
            documentFromEs.setIsHidden(hide);

            String doc = JSONUtils.toJSON(documentFromEs, JsonInclude.Include.NON_NULL);
            esService.updateDocument(doc, ElasticConstants.SOCIAL_ENGAGE_V2_FEED.getName(), documentFromEs.getRawFeedId());

            auditEngageActions(action, request, Constants.SUCCESS, null);
            if(Objects.nonNull(request.getChannel())
                    && request.getChannel().equalsIgnoreCase(SocialChannel.TIKTOK.getName())
                    && (EngageV2FeedTypeEnum.COMMENT.name().equalsIgnoreCase(documentFromEs.getType())
                            || AD_COMMENT.name().equalsIgnoreCase(documentFromEs.getType()))) {
                updatePostCommentCount(documentFromEs, !hide, 1L, false);
            }
        } catch (Exception ex) {
            auditEngageActions(action, request, Constants.FAILED, ex.getMessage());
            throw ex;
        }
    }

    @Override
    public void blockUserFromPage(SocialEngageObjectRequest request,Integer accountId) throws IOException {

        try {
            SocialEngageV2 execute = engageFactory.getSocialEngageChannel(request.getChannel())
                    .orElseThrow(() -> new IllegalArgumentException(INVALID_REQUEST));

            ExternalServiceEvent blockUserEvent = new ExternalServiceEvent();
            if(Boolean.TRUE.equals(request.getIsBlocked())) {
                execute.blockUserFromPage(request,blockUserEvent);
            } else {
                execute.unBlockUserFromPage(request,blockUserEvent);
            }
            // update SQL entry
            List<EngageUserDetails> userDetailsList = engageUserDetailsRepo.findByAuthorIdAndPageId(request.getAuthorId(), request.getPageId());
            if(CollectionUtils.isEmpty(userDetailsList)) {
                LOGGER.info("No entry found for engage user for request {}", request);
                return;
            }
            EngageUserDetails userDetails=userDetailsList.get(0);
            if(Boolean.TRUE.equals(request.getPublishEvent())){
                blockUserEvent.setEnterpriseId(accountId);
                blockUserEvent.setBlocked(request.getIsBlocked());
                blockUserEvent.setSocialUserId(request.getAuthorId());
                blockUserEvent.setSource(request.getChannel());
                blockUserEvent.setName(userDetails.getAuthorName());
                kafkaProducerService.sendObjectV1(KafkaTopicEnum.SOCIAL_SEND_BLOCK_EVENT.getName(),blockUserEvent);
            }
            if(Boolean.TRUE.equals(request.getIsBlocked())) {
                userDetails.setIsBlocked(1);
                auditEngageActions(EngageActionsEnum.BLOCK_USER.name(), request, Constants.SUCCESS, null);
            } else {
                userDetails.setIsBlocked(0);
                auditEngageActions(EngageActionsEnum.UNBLOCK_USER.name(), request, Constants.SUCCESS, null);
            }
            engageUserDetailsRepo.saveAndFlush(userDetails);
        } catch (SocialException e) {
            auditEngageActions(Boolean.TRUE.equals(request.getIsBlocked()) ? EngageActionsEnum.BLOCK_USER.name() :
                    EngageActionsEnum.UNBLOCK_USER.name(), request, Constants.FAILED, e.getMessage());
          throw new SocialException(e.getMessage(), e.getCode());
        } catch (Exception ex) {
            auditEngageActions(Boolean.TRUE.equals(request.getIsBlocked()) ? EngageActionsEnum.BLOCK_USER.name() :
                    EngageActionsEnum.UNBLOCK_USER.name(), request, Constants.FAILED, ex.getMessage());
            throw ex;
        }
    }

    @Override
    public void unblockUserFromPage(SocialEngageObjectRequest request) throws IOException {

        try {
            SocialEngageV2 execute = engageFactory.getSocialEngageChannel(request.getChannel())
                    .orElseThrow(() -> new IllegalArgumentException(INVALID_REQUEST));

            ExternalServiceEvent blockUserEvent = new ExternalServiceEvent();
            execute.unBlockUserFromPage(request,blockUserEvent);

            // update es doc

            Integer objectId = request.getRawFeedId();
            EngageNotificationDetails documentFromEs = getFeedDocumentFromEs(objectId);

            if(Objects.isNull(documentFromEs)) {
                LOGGER.info(NO_COMMENT_DOC_FOUND_LOG_MSG, objectId);
                return;
            }

            documentFromEs.setIsBlocked(false);

            String doc = JSONUtils.toJSON(documentFromEs, JsonInclude.Include.NON_NULL);
            esService.updateDocument(doc, ElasticConstants.SOCIAL_ENGAGE_V2_FEED.getName(), documentFromEs.getRawFeedId());

            auditEngageActions(EngageActionsEnum.UNBLOCK_USER.name(), request, Constants.SUCCESS, null);
        } catch (Exception ex) {
            auditEngageActions(EngageActionsEnum.UNBLOCK_USER.name(), request, Constants.FAILED, ex.getMessage());
            throw ex;
        }

    }

    private void updateIsDeletedOriginalComment(String commentId) {
        try {
            UpdateByQueryRequest updateRequest = new UpdateByQueryRequest(ElasticConstants.SOCIAL_ENGAGE_V2_FEED.getName());
            updateRequest.setQuery(QueryBuilders.matchPhraseQuery("feedId.keyword", commentId));

            Map<String, Object> data = new HashMap<>();
            data.put(VALUE, true);
            Script inline = new Script(ScriptType.INLINE, PAINLESS, SOURCE_DELETE, data);
            updateRequest.setScript(inline);
            LOGGER.info(QUERY_DOC_LOG_MSG , updateRequest);

            BulkByScrollResponse response = esService.updateByQueryRequest(updateRequest);

            long updatedDocuments = response.getUpdated();
            LOGGER.info(UPDATED_DOC_LOG_MSG , updatedDocuments);
        } catch (Exception ex) {
            LOGGER.info("Error while updating original comment {}", commentId);
        }
    }

    private long updateIsDeletedEsDocumentByEventParentId(String feedId, boolean isParentComment) {
        LOGGER.info("[updateIsDeletedEsDocumentByEventParentId] method called with feedId : {}", feedId);
        try {
            // Get all feedIds in the hierarchy
            Set<String> allFeedIds = new HashSet<>();
            if (isParentComment) {
                collectChildFeedIds(feedId, allFeedIds);
            } else {
                allFeedIds.add(feedId);
            }

            LOGGER.info("[updateIsDeletedEsDocumentByEventParentId] All feedIds to be updated: {}", allFeedIds);


            if (!allFeedIds.isEmpty()) {
                UpdateByQueryRequest updateRequest = new UpdateByQueryRequest(ElasticConstants.SOCIAL_ENGAGE_V2_FEED.getName());
                updateRequest.setQuery(QueryBuilders.termsQuery("feedId.keyword", allFeedIds));

                Map<String, Object> params = new HashMap<>();
                params.put(VALUE, true);
                Script inlineScript = new Script(ScriptType.INLINE, PAINLESS, SOURCE_DELETE, params);

                updateRequest.setScript(inlineScript);

                BulkByScrollResponse response = esService.updateByQueryRequest(updateRequest);

                long updatedDocuments = response.getUpdated();
                LOGGER.info("[updateIsDeletedEsDocumentByEventParentId] Number of documents updated: {}", updatedDocuments);
                return updatedDocuments;
            } else {
                LOGGER.warn("[updateIsDeletedEsDocumentByEventParentId] No feedIds found to update for feedId: {}", feedId);
            }
        } catch (Exception ex) {
            LOGGER.info("No parentId found to be updated {}", feedId);
        }
        return 0;
    }

    private void collectChildFeedIds(String parentFeedId, Set<String> allFeedIds) {
        try {
            Queue<String> queue = new LinkedList<>();
            queue.add(parentFeedId);
            allFeedIds.add(parentFeedId);

            while (!queue.isEmpty()) {
                String currentFeedId = queue.poll();

                SearchRequest searchRequest = new SearchRequest(ElasticConstants.SOCIAL_ENGAGE_V2_FEED.getName());
                searchRequest.source(new SearchSourceBuilder()
                        .query(QueryBuilders.termQuery("eventParentId.keyword", currentFeedId))
                        .fetchSource("feedId", null)
                        .size(500));

                SearchResponse searchResponse = esService.search(searchRequest);
                SearchHits hits = searchResponse.getHits();

                for (SearchHit hit : hits.getHits()) {
                    String childFeedId = (String) hit.getSourceAsMap().get("feedId");
                    if (allFeedIds.add(childFeedId)) {
                        queue.add(childFeedId); // Add to queue instead of recursive call
                    }
                }
            }
        } catch (Exception ex) {
            LOGGER.error("[collectChildFeedIds] Error fetching child feedIds for parentFeedId {}: {}", parentFeedId, ex.getMessage(), ex);
        }
    }

    private void updateIsDeletedEsDocumentByPostId(String postId, Boolean isDeleted) {
        try {
            UpdateByQueryRequest updateRequest = new UpdateByQueryRequest(ElasticConstants.SOCIAL_ENGAGE_V2_FEED.getName());
            updateRequest.setQuery(QueryBuilders.termQuery(POST_ID_ES.concat(KEYWORD), postId));

            Map<String, Object> data = new HashMap<>();
            data.put(VALUE, isDeleted);
            Script inline = new Script(ScriptType.INLINE, PAINLESS, SOURCE_DELETE, data);
            updateRequest.setScript(inline);
            LOGGER.info(QUERY_DOC_LOG_MSG , updateRequest);

            BulkByScrollResponse response = esService.updateByQueryRequest(updateRequest);

            long updatedDocuments = response.getUpdated();
            LOGGER.info(UPDATED_DOC_LOG_MSG , updatedDocuments);


        } catch (Exception ex) {
            LOGGER.info("No postId found to be updated {}", postId);
        }

    }

    private List<EngageNotificationDetails> searchPostById( String keyName, String value) {
        List<EngageNotificationDetails> engageNotificationDetailsList = new ArrayList<>();
        try {
            SearchRequest searchRequest = new SearchRequest(ElasticConstants.SOCIAL_ENGAGE_V2_FEED.getName());
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();

            BoolQueryBuilder b = new BoolQueryBuilder();
            b.must(QueryBuilders.termQuery(keyName, value));
            b.must(QueryBuilders.termQuery(IS_DELETED_ES, false));
            b.must(QueryBuilders.termQuery("type", EngageV2FeedTypeEnum.POST.name().toLowerCase()));

            searchSourceBuilder.size(1000);//1000
            searchSourceBuilder.query(b);
            searchRequest.source(searchSourceBuilder);
            SearchResponse response = esService.search(searchRequest);
            SearchHit[] searchHits = response.getHits().getHits();
            if(searchHits.length > 0) {
                for(SearchHit hit : searchHits) {
                    EngageNotificationDetails adPostDetails = JSONUtils.fromJSON(hit.getSourceAsString(), EngageNotificationDetails.class);
                    engageNotificationDetailsList.add(adPostDetails);
                }
                return engageNotificationDetailsList;
            }
        } catch (Exception ex) {
            LOGGER.info(ES_FETCH_ERROR_LOG_MSG, ex);
        }
        return engageNotificationDetailsList;
    }

    @Override
    public void syncProfileData(EngageSyncPostRequest request) {
        LOGGER.info("Engage sync post request received to sync profile data {}", request);

        EngageFeedDetails feedDetails = engageFeedDetailsRepo.findTopByPageIdAndTypeOrderByFeedDateDesc(request.getPageId(), EngageV2FeedTypeEnum.POST.name());

        LOGGER.info("No existing record found for request {}", request);
        SocialEngageV2 execute = engageFactory.getSocialEngageChannel(request.getChannel())
                .orElseThrow(() -> new IllegalArgumentException(INVALID_REQUEST));

        // fetch latest 10 post
        SocialTimeline data =  execute.getFeedData(request.getPageId(), feedDetails,request.getType());

        // for comments sync
        /*if(SocialChannel.FACEBOOK.getName().equalsIgnoreCase(request.getChannel())
                || SocialChannel.TWITTER.getName().equalsIgnoreCase(request.getChannel())) {
            List<EngageNotificationDetails> posts = searchPostById("pageId", request.getProfileId());
            if(CollectionUtils.isNotEmpty(posts)) {
                for(EngageNotificationDetails details : posts) {
                    EngageCommentRequest req = new EngageCommentRequest();
                    req.setProfileId(request.getProfileId());
                    req.setObjectId(details.getFeedId());
                    req.setConversationId(details.getFeedId());
                    // comments fetched
                    List<EngageNotificationDetails> comments = execute.getCommentData(req);
                    if(CollectionUtils.isEmpty(comments)){
                        continue;
                    }
                    // comments save to DB
                    saveEngageCommentsAndProcessToES(comments, request.getChannel(), request.getProfileId(), details.getFeedId(), data.getProfileName(), null, false,false);
                }
            } else {
                LOGGER.info("No existing post found in ES for {}", request);
            }
        }*/

        if(SocialChannel.LINKEDIN.getName().equalsIgnoreCase(request.getChannel()) ||
                SocialChannel.YOUTUBE.getName().equalsIgnoreCase(request.getChannel())){
            LOGGER.info("Data saved to ES and DB  for request {}" , request);
            return;
        }
        if(Objects.isNull(data) || CollectionUtils.isEmpty(data.getFeeds())) {
            LOGGER.info("No data found. exiting the process for request {}" , request);
            return;
        }
        //In case of IG, we need to show comments initially as posts are not shown here
        boolean isFreshRequest = SocialChannel.INSTAGRAM.getName().equalsIgnoreCase(request.getChannel()) ||
                SocialChannel.FACEBOOK.getName().equalsIgnoreCase(request.getChannel());

        for(Feed feed: data.getFeeds()) {
            // save post details
            saveEngageFeedAndProcessToES(feed,request.getChannel(),request.getPageId(), data.getPageName(), EngageV2FeedTypeEnum.POST.name(),false);
            // fetch and save comments
            EngageCommentRequest req = new EngageCommentRequest();
            if(request.getChannel().equalsIgnoreCase(SocialChannel.FACEBOOK.getName()) || request.getChannel().equalsIgnoreCase(SocialChannel.INSTAGRAM.getName())
            || request.getChannel().equalsIgnoreCase(SocialChannel.TWITTER.getName())) {
                req.setPageId(request.getPageId());
                req.setObjectId(feed.getFeedId());
                req.setConversationId(feed.getConversationId());
            }
            // comments fetched
            List<EngageNotificationDetails> comments = execute.getCommentData(req);
            if(CollectionUtils.isEmpty(comments)){
                continue;
            }
            LOGGER.info("Inside sync Engage data : postData : {} comment size:{}", feed.getFeedId(), comments.size());
            // comments save to DB
            saveEngageCommentsAndProcessToES(comments, request.getChannel(), request.getPageId(), feed.getFeedId(), data.getPageName(),null, isFreshRequest,false);
        }
    }

    private void sendDataToNlp(String text, String feedId, String pageId, Integer sourceId, Integer enterpriseId, Integer businessId, Boolean isHidden, Integer rawFeedId) {
        LanguageDetectDTO languageDetectDTO = new LanguageDetectDTO();
        languageDetectDTO.setText(text);
        languageDetectDTO.setRawFeedId(rawFeedId);
        languageDetectDTO.setFeedId(feedId);
        languageDetectDTO.setPageId(pageId);
        languageDetectDTO.setSourceId(sourceId);
        languageDetectDTO.setLanguage("en");
        languageDetectDTO.setEnterpriseId(enterpriseId);
        languageDetectDTO.setBusinessId(businessId);
        languageDetectDTO.setIsHidden(isHidden);
        nlpService.detectLanguage(languageDetectDTO);
    }

    @Override
    public void saveContentDetails(EngageNotificationDetails request) {
        try {
            SocialEngageV2 execute = engageFactory.getSocialEngageChannel(request.getChannel())
                    .orElseThrow(() -> new IllegalArgumentException(INVALID_REQUEST));

            // fill additional details
            checkAdditionalParameters(request);
            // check usage
            request = execute.prepareAdditionalParamentToEs(request);
            if(Objects.isNull(request)){
                LOGGER.info("Request is null");
                return;
            }
            EngageBusinessDetails pageBusinessDetails = execute.getChannelEnterpriseIdByPageId(request.getPageId());
            if(Objects.isNull(pageBusinessDetails) || Objects.isNull(pageBusinessDetails.getEnterpriseId()) || CollectionUtils.isEmpty(pageBusinessDetails.getBusinessIds())) {
                LOGGER.info("No business details found for request {}, exiting feed counter update process", request);
                return;
            }

            boolean isReply = EngageV2FeedSubTypeEnum.REPLY.name().equalsIgnoreCase(request.getSubType());
            boolean isComment = !isReply && COMMENT.name().equalsIgnoreCase(request.getType());

            if (!request.isAddedFromDashboard() && COMMENT.name().equalsIgnoreCase(request.getType())) {
                if (isReply) {
                    updateParentCommentCount(request.getTopParentFeedId(), request.getPageId(), true,1);
                    if (isEligibleForPostCountUpdate(request.getSourceId())) {
                        updatePostCommentCount(request, true, 1L, true);
                    }
                } else if (isComment) {
                    updatePostCommentCount(request, true, 1L, true);
                }
            }
            uploadMediaToS3(request, pageBusinessDetails);
            uploadEngageContentToES(request);
            // add user details in DB
            upsertUserDetails(request.getAuthorId(), request.getPageId(), request.getAuthorName(), request.getSourceId());

            //Email Alerts
            sendEmailAlerts(request,pageBusinessDetails);

            Integer smallEnterpriseId = businessCoreService.getBusinessId(pageBusinessDetails.getEnterpriseId());
            LOGGER.info("details found for request {} enterpriseId : {}", request, smallEnterpriseId);
            boolean isVisible = Objects.nonNull(request.getHideOnThread()) && !request.getHideOnThread();
            if(isVisible) {
                if(!Objects.equals(request.getType(),EngageV2FeedTypeEnum.COMMENT.name())
                        || !StringUtils.isNotEmpty(request.getText())) {
                    insertNewNotificationInFirebase(smallEnterpriseId, pageBusinessDetails.getBusinessIds().get(0));
                }
                upsertEsCounterByEnterpriseId(pageBusinessDetails,smallEnterpriseId, request.getChannel());
            }
            if(request.getType().equalsIgnoreCase(EngageV2FeedTypeEnum.COMMENT.name()) && org.apache.commons.lang3.StringUtils.isNotEmpty(request.getText())) {
                sendDataToNlp(request.getText(), request.getFeedId(), request.getPageId(), request.getSourceId(),smallEnterpriseId, pageBusinessDetails.getBusinessIds().get(0),!isVisible, request.getRawFeedId());
            }

        } catch (Exception ex) {
            LOGGER.info("Something went wrong while saving data to ES ", ex);
        } finally {
            if(Objects.nonNull(request)){
                EngageFeedDetails details =  engageFeedDetailsRepo.findFirstByFeedIdAndPageId(request.getFeedId(), request.getPageId());
                if (Objects.nonNull(details)) {
                    String metaData = JSONUtils.toJSON(request, JsonInclude.Include.NON_NULL);
                    details.setMetaData(metaData);
                    engageFeedDetailsRepo.saveAndFlush(details);
                }else{
                    LOGGER.info("EngageFeedDetails is null for request:{}",request);
                }
            }else{
                LOGGER.info("Request can not be null");
            }
        }
    }

    private void updateCommentCountOnDeletion(EngageNotificationDetails document, long count) {
        try {
            LOGGER.info("Update comment count deletion for postId: {}, pageId:{} and feedId: {} with count :{}", document.getPostId(),
                    document.getPageId(), document.getFeedId(), count);
            boolean isReply = EngageV2FeedSubTypeEnum.REPLY.name().equalsIgnoreCase(document.getSubType());
            boolean isComment = !isReply && COMMENT.name().equalsIgnoreCase(document.getType());
            boolean isInstagramOrLinkedIn = isEligibleForPostCountUpdate(document.getSourceId());
            if (isReply) {
                String topParentFeedId = null;
                if (Objects.nonNull(document.getTopParentFeedId())) {
                    topParentFeedId = document.getTopParentFeedId();
                } else {
                    EngageNotificationDetails engageNotificationDetails = engageConverterService.findTopmostParentCommentRecursively(
                            document.getFeedId(), document.getPageId(), document.getEventParentId());
                    if(Objects.nonNull(engageNotificationDetails)) {
                        topParentFeedId = engageNotificationDetails.getFeedId();
                    }
                }
                if(Objects.nonNull(topParentFeedId)) {
                    updateParentCommentCount(topParentFeedId, document.getPageId(),false, count);
                }
                if (isInstagramOrLinkedIn) {
                    updatePostCommentCount(document, false, count, false);
                }
            } else if (isComment) {
                updatePostCommentCount(document, false, count, false);
            }
        } catch (IOException ioException) {
            LOGGER.info("IOException occurred while updating the ES index :{}", document.getRawFeedId(), ioException);
        }
    }

    private int getCommentCount(boolean isAdded, EngageNotificationDetails engageNotification,long count) {
        return Objects.isNull(engageNotification.getCommentCount())
                ? (isAdded ? 1 : 0)
                : (isAdded ? engageNotification.getCommentCount() + 1 : Math.max(engageNotification.getCommentCount() - Math.toIntExact(count), 0));
    }

    @Override
    public void updateContentDetails(EngageNotificationDetails request){
        if(Objects.isNull(request.getFeedId())){
            LOGGER.info("[Update Engage] FeedId can not be null");
            return;
        }
        try {
            List<EngageNotificationDetails> esDocs =  fetchEsDocByFeedId(request.getFeedId());
            if(CollectionUtils.isEmpty(esDocs)){
                LOGGER.info("[Update Engage] No document found for feedId {}", request.getFeedId());
                return;
            }
            EngageNotificationDetails esDoc = esDocs.get(0);
            if(Objects.nonNull(request.getLikeCount())){
                esDoc.setLikeCount(request.getLikeCount());
            }
            if(Objects.nonNull(request.getCommentCount())){
                esDoc.setCommentCount(request.getCommentCount());
            }
            upsertEngageEsDocument(esDoc);
            EngageFeedDetails engageFeedDetails = engageFeedDetailsRepo.findById(esDoc.getRawFeedId());
            if(Objects.nonNull(engageFeedDetails)) {
                engageFeedDetails.setMetaData(JSONUtils.toJSON(esDoc));
                engageFeedDetailsRepo.save(engageFeedDetails);
            }
        } catch (Exception e) {
            LOGGER.info("exception occurred while updating media url in es for request: {}, error: {}",request, e.getMessage());
        }
    }

    private void sendEmailAlerts(EngageNotificationDetails feedDetails, EngageBusinessDetails businessDetails) {
        try{
            LOGGER.info("Processing email Request for feed id {}",feedDetails.getFeedId());
            if(isApplicableforEmailAlert(feedDetails)) {
                if(Objects.isNull(businessDetails) || businessDetails.getBusinessIds().isEmpty())
                {
                    LOGGER.info("No location found for feedId {}",feedDetails.getFeedId());
                    return;
                }
                EmailAlertDetail emailAlertDetail = new EmailAlertDetail();
                emailAlertDetail.setFeedId(String.valueOf(feedDetails.getRawFeedId()));
                emailAlertDetail.setEngageBusinessDetails(businessDetails);
                kafkaProducerService.sendObjectV1(KafkaTopicEnum.SOCIAL_ENGAGE_EMAIL_ALERT.getName(), emailAlertDetail);
            }
            else {
                LOGGER.info("Event with feedId {} not applicable for email alert",feedDetails.getFeedId());
            }
        }
        catch(Exception e)
        {
            LOGGER.info("Exception occurred while pushing email alert to kafka",e);
        }
    }

    private boolean isApplicableforEmailAlert(EngageNotificationDetails feedDetails) {
        return Objects.nonNull(feedDetails) && Objects.nonNull(feedDetails.getType()) &&
                (Objects.isNull(feedDetails.getIsRealTimeNotification()) || feedDetails.getIsRealTimeNotification()) && (
                (feedDetails.getType().equalsIgnoreCase(EngageV2FeedTypeEnum.COMMENT.name())
                        && Objects.nonNull(feedDetails.getIsAdminComment())
                        && Boolean.FALSE.equals(feedDetails.getIsAdminComment()))
                        || (feedDetails.getType().equalsIgnoreCase(EngageV2FeedTypeEnum.POST.name()) &&
                        (Objects.nonNull(feedDetails.getSubType()) && feedDetails.getSubType().equalsIgnoreCase(EngageV2FeedSubTypeEnum.MENTION.name())))
                        || (feedDetails.getType().equalsIgnoreCase(EngageV2FeedTypeEnum.RETWEET.name()))
                        || (feedDetails.getType().equalsIgnoreCase(EngageV2FeedTypeEnum.QUOTE_TWEET.name()))
                        || (feedDetails.getType().equalsIgnoreCase(EngageV2FeedTypeEnum.FOLLOW.name()))
                        || (feedDetails.getType().equalsIgnoreCase(EngageV2FeedTypeEnum.MESSAGE.name())
                        && (Objects.isNull(feedDetails.getSubType()) || feedDetails.getSubType().equalsIgnoreCase(EngageV2FeedSubTypeEnum.MENTION.name())))
                        || (feedDetails.getType().equalsIgnoreCase(AD_COMMENT.name()))
        );
    }

    private void checkAdditionalParameters(EngageNotificationDetails req) {
        try {
            if(Objects.nonNull(req.getAuthorId()) && req.getAuthorId().equalsIgnoreCase(req.getPageId()) && EngageV2FeedTypeEnum.COMMENT.name().equalsIgnoreCase(req.getType())) {
                req.setHideOnThread(true);
            }
            if(Objects.isNull(req.getAuthorName())){
                if(StringUtils.isNotEmpty(req.getChannel()) && SocialChannel.getSocialChannelByName(req.getChannel()) != null){
                    req.setAuthorName(SocialChannel.getSocialChannelByName(req.getChannel()).getLabel()+" User");
                }
            }
            // check parentText
            if(EngageV2FeedTypeEnum.POST.name().equalsIgnoreCase(req.getType())) {
                req.setParentPostText(req.getText());
            } else if(EngageV2FeedTypeEnum.COMMENT.name().equalsIgnoreCase(req.getType())
                        || AD_COMMENT.name().equalsIgnoreCase(req.getType())) {

                EngageNotificationDetails postDetails = getFeedDocumentFromEs(req.getRawFeedId());
                if(Objects.isNull(postDetails)) {
                    LOGGER.info("No post detail found for post in ES, fetch post details to set parent text {}", req.getPostId());
                    return;
                }
                if (Objects.nonNull(postDetails.getText()) && postDetails.getText().length() > 300) {
                    // Select the first 300 characters
                    String selectedString = postDetails.getText().substring(0, 300);
                    req.setParentPostText(selectedString);
                } else {
                    req.setParentPostText(postDetails.getText());
                }
            }

        } catch (Exception ex) {
            LOGGER.info("Something went wrong while updating ES parameters for request {} with error {}", req, ex);
        }
    }

    private void upsertUserDetails(String userId, String pageId, String userName, Integer sourceId) {
        try {
            List<EngageUserDetails> userDetailsList =  engageUserDetailsRepo.findByAuthorIdAndPageId(userId, pageId);
            if(CollectionUtils.isEmpty(userDetailsList)) {
                LOGGER.info("No user found for pageId {} with authorId {}", pageId, userId);
                EngageUserDetails user = new EngageUserDetails();
                user.setAuthorId(userId);
                user.setAuthorName(userName);
                user.setPageId(pageId);
                user.setIsBlocked(0);
                user.setIsFollowed(0);
                user.setSourceId(sourceId);

                engageUserDetailsRepo.saveAndFlush(user);
            }
        } catch (Exception ex) {
            LOGGER.info("Could not save user details for userId {} with error {}", userId, ex);
        }
    }

    private void uploadMediaToS3(EngageNotificationDetails request, EngageBusinessDetails pageBusinessDetails) {
        List<MessageAttachment> attachments = new ArrayList<>();
        try {
            String enterpriseId = String.valueOf(pageBusinessDetails.getEnterpriseId());
            List<MediaAsset> mediaAssetList = new ArrayList<>();
            if(StringUtils.isNotEmpty(request.getAuthorProfileImage()) && isValidUrl(request.getAuthorProfileImage())) {
                mediaAssetList.add(createMediaAssetForEngage(request.getAuthorProfileImage(), request.getRawFeedId(), MediaAssetMediaType.IMAGE));
            }

            if(CollectionUtils.isNotEmpty(request.getImageUrls())) {
                for(Object url:  request.getImageUrls()) {
                    String u = (String) url;
                    if(StringUtils.isNotEmpty(u)) {
                        mediaAssetList.add(createMediaAssetForEngage(u, request.getRawFeedId(), MediaAssetMediaType.IMAGE));
                    }
                }
            }

            if(CollectionUtils.isNotEmpty(request.getVideoUrls())) {
                for(Object url:  request.getVideoUrls()) {
                    String u = (String) url;
                    if(StringUtils.isNotEmpty(u)) {
                        mediaAssetList.add(createMediaAssetForEngage(u, request.getRawFeedId(), MediaAssetMediaType.VIDEO));
                    }
                }
            }
            if(CollectionUtils.isNotEmpty(request.getThumbnails())) {
                for(Object url:  request.getThumbnails()) {
                    String u = (String) url;
                    if(StringUtils.isNotEmpty(u)) {
                        mediaAssetList.add(createMediaAssetForEngage(u, request.getRawFeedId(), MediaAssetMediaType.IMAGE));
                    }
                }
            }
            if(Objects.nonNull(request.getSubEvent())) {
                if(StringUtils.isNotEmpty(request.getSubEvent().getAuthorProfileImage())) {
                    mediaAssetList.add(createMediaAssetForEngage(request.getSubEvent().getAuthorProfileImage(), request.getRawFeedId(), MediaAssetMediaType.IMAGE));
                }
                if (CollectionUtils.isNotEmpty(request.getSubEvent().getImageUrls())) {
                    for (Object url : request.getSubEvent().getImageUrls()) {
                        String u = (String) url;
                        if(StringUtils.isNotEmpty(u)) {
                            mediaAssetList.add(createMediaAssetForEngage(u, request.getRawFeedId(), MediaAssetMediaType.IMAGE));
                        }
                    }
                }
                if (CollectionUtils.isNotEmpty(request.getSubEvent().getVideoUrls())) {
                    for (Object url : request.getSubEvent().getVideoUrls()) {
                        String u = (String) url;
                        if(StringUtils.isNotEmpty(u)) {
                            mediaAssetList.add(createMediaAssetForEngage(u, request.getRawFeedId(), MediaAssetMediaType.IMAGE));
                        }
                    }
                }
            }

            mediaAssetRepoService.saveAllAndFlush(mediaAssetList);
            String callbackUrl = CacheManager.getInstance()
                    .getCache(SystemPropertiesCache.class).getUploadPicturesqueMediaUploadCallBackUrl();
            for(MediaAsset mediaAsset: mediaAssetList) {
                try {
                    // no need to update the video URL for tiktok as it is not a CDN URL
                    // eg: https://www.tiktok.com/player/v1/7485053550842678545
                    if(mediaAsset.getEntityType().equalsIgnoreCase("ENGAGE") &&
                            mediaAsset.getMediaUrl().contains("tiktok.com/player") &&
                            mediaAsset.getMediaType().equalsIgnoreCase("VIDEO")) {
                        continue;
                    }
                    picturesqueService(mediaAsset.getMediaUrl(), enterpriseId, mediaAsset.getId(), callbackUrl);
                } catch (Exception e) {
                    LOGGER.info("exception occurred for uploading image on picturesque for url: {}, enterpriseId: {}, error: {}", e.getMessage());
                }
            }
        } catch (Exception ex) {
            LOGGER.info("Something went while uploading file to server ", ex);
        }
    }

    MediaAsset createMediaAssetForEngage(String url, Integer rawFeedId, MediaAssetMediaType mediaType) {
        MediaAsset mediaAsset = MediaAsset.builder()
                .entityId(String.valueOf(rawFeedId))
                .entityType(MediaAssetEntityType.ENGAGE.getName())
                .mediaUrl(url)
                .mediaType(mediaType.getName())
                .build();
        return mediaAsset;
    }

    private void sendEventToInbox(EngageNotificationDetails request, List<MessageAttachment> attachments, EngageBusinessDetails pageBusinessDetails) {
        InboxMessageRequest inboxRequest = new InboxMessageRequest();
        inboxRequest.setMessengerContactId(request.getMessengerContactId());
        inboxRequest.setMessageId(request.getFeedId());
        inboxRequest.setBusinessId(pageBusinessDetails.getBusinessIds() != null ?  pageBusinessDetails.getBusinessIds().get(0) : null);
        inboxRequest.setChannel(request.getChannel());
        inboxRequest.setAttachments(attachments);
        kafkaProducerService.sendObjectV1(KafkaTopicEnum.MESSENGER_MEDIA_BIRDEYE_CDN_URL_UPDATE.getName(), inboxRequest);
        //messenger_media_birdeye_cdn_url_update
    }
    private List<MessageAttachment> createMediaEventForInbox(MediaData m, String url, List<MessageAttachment> attachments) {
        MessageAttachment attach = new MessageAttachment();
        attach.setA_url(url);
        attach.setCdn_url(m.getMediaUrl());
        attachments.add(attach);
        return attachments;
    }

    private void picturesqueService(String url, String id, Integer callbackId, String callbackUrl) {
        LOGGER.info("picturesqueService call to upload media asset with url: {}, media asset id: {} and callbackUrl: {}", url, callbackId, callbackUrl);
        try {
            PicturesqueMediaUploadRequest req = new PicturesqueMediaUploadRequest();
            req.setImgPath(url);
            req.setBusinessNumber(id);
            req.setSource("other");
            req.setMimeType("");
            req.setReqMetaData(true);
            req.setCallbackUrl(callbackUrl);
            req.setCallbackId(callbackId);
            picturesqueService.uploadMediaWithMetaDataInAsync(req);
        } catch (Exception ex) {
            LOGGER.info("Something went wrong while uploading media to service for url {} with error {}", url, ex);
        }
    }

    private MediaData getMediaDataForPicturesqueCallback(PicturesqueMediaCallback callback, String id) {
        String metadata = JSONUtils.toJSON(callback, JsonInclude.Include.NON_NULL);
        MediaData m = new MediaData(callback.getMediaUrl(), metadata);
        return m;
    }

    @Async
    void upsertEsCounterByEnterpriseId(EngageBusinessDetails details, Integer smallEnterpriseId, String channel) {
        try {
            String indexName = ElasticConstants.SOCIAL_ENGAGE_STATS.getName();
            Long enterpriseId = details.getEnterpriseId();
            if(Objects.isNull(SocialChannel.getSocialChannelByName(channel))) {
                return ;
            }
            Integer sourceId = SocialChannel.getSocialChannelByName(channel).getId();

            BusinessLiteUserDTO userDTO = businessCoreService.getEnterpriseUsersWithLocation(smallEnterpriseId, details.getBusinessIds());
            if(Objects.isNull(userDTO) || CollectionUtils.isEmpty(userDTO.getUserDetailsList()) ) {
                LOGGER.info("No user details found for enterprise {}", details);
                return;
            }


            FeedCounterDetails feedCounterDetails =  getEngageStatsDocumentFromEs(enterpriseId);

            if (Objects.nonNull(feedCounterDetails)) {
                // Document exists, update counter
                List<FeedUserCounterDetails> userCounterDetailsList = feedCounterDetails.getDetails();

                for(User user:  userDTO.getUserDetailsList()) {
                    Boolean userAndSourcePresent = false;
                        for(FeedUserCounterDetails v : userCounterDetailsList) {
                            if(v.getUserId().equals(user.getId()) && sourceId.equals(v.getSourceId())) {
                                v.setCounter(v.getCounter() + 1);
                                userAndSourcePresent = true;
                                break;
                            }
                        }

                   if(!userAndSourcePresent) {
                       FeedUserCounterDetails counterDetails = new FeedUserCounterDetails();
                       counterDetails.setUserId(user.getId());
                       counterDetails.setSourceId(sourceId);
                       counterDetails.setCounter(1);
                       userCounterDetailsList.add(counterDetails);
                   }
                }

                feedCounterDetails.setDetails(userCounterDetailsList);

                String doc = JSONUtils.toJSON(feedCounterDetails, JsonInclude.Include.NON_NULL);
                esService.updateDocument(doc, indexName, enterpriseId.toString());
            } else {

                FeedCounterDetails newFeedCounterDetails = new FeedCounterDetails();
                List<FeedUserCounterDetails> userCounterDetailsList = new ArrayList<>();
                // Document does not exist, create new document

                for(User user : userDTO.getUserDetailsList()) {
                    FeedUserCounterDetails counterDetails = new FeedUserCounterDetails();
                    counterDetails.setUserId(user.getId());
                    counterDetails.setSourceId(sourceId);
                    counterDetails.setCounter(1);
                    userCounterDetailsList.add(counterDetails);
                }

                newFeedCounterDetails.setEnterpriseId(enterpriseId.toString());
                newFeedCounterDetails.setDetails(userCounterDetailsList);


                String doc = JSONUtils.toJSON(newFeedCounterDetails, JsonInclude.Include.NON_NULL);

                esService.addDocument(doc, indexName , enterpriseId.toString());
            }


        } catch (Exception ex) {
            LOGGER.info("something went wrong while saving counter details for request {}", details);
        }
    }

    @Override
    public void savePostDetails(FreshPostNotificationRequest request) {
        LOGGER.info("Engage sync post request received {}", request);

        SocialEngageV2 execute = engageFactory.getSocialEngageChannel(request.getChannel())
                .orElseThrow(() -> new IllegalArgumentException(INVALID_REQUEST));

        // fetch post details
        Feed feed =  execute.getPostDetails(request);

        if(Objects.isNull(feed)) {
            LOGGER.info("No data found. exiting the process for request {}" , request);
            return;
        }

        boolean result = execute.validateImageUrls(feed.getImages());
        if (!result) {
            samayService.sendRetryRequestToSamayForPostDetails(request);
            return;
        }


        // save post details
        saveEngageFeedAndProcessToES(feed, request.getChannel(), request.getPageId(), feed.getPageName(), request.getType(), false);
        // fetch and save comments
        EngageCommentRequest req = new EngageCommentRequest();
        if (request.getChannel().equalsIgnoreCase(SocialChannel.FACEBOOK.getName()) ||
                request.getChannel().equalsIgnoreCase(SocialChannel.INSTAGRAM.getName()) ||
                request.getChannel().equalsIgnoreCase(SocialChannel.LINKEDIN.getName()) ||
                request.getChannel().equalsIgnoreCase(SocialChannel.TWITTER.getName()) ||
                request.getChannel().equalsIgnoreCase(SocialChannel.TIKTOK.getName())) {
            req.setPageId(request.getPageId());
            req.setObjectId(feed.getFeedId());
            req.setConversationId(feed.getConversationId());
        }
        // comments fetched
        List<EngageNotificationDetails> comments = execute.getCommentData(req);


        if (!Objects.isNull(request.getNewCommentId())){
                boolean validateCommentDetails = execute.validateCommentDetails(comments, request.getNewCommentId());

            if (!validateCommentDetails) {

                samayService.sendRetryRequestToSamayForPostDetails(request);
                return;
            }
        }

        //In case of IG and FB, we need to show comments initially as posts are not shown here
        boolean isFreshRequest = SocialChannel.INSTAGRAM.getName().equalsIgnoreCase(request.getChannel()) ||
                SocialChannel.FACEBOOK.getName().equalsIgnoreCase(request.getChannel());
//		// comments save to DB
        saveEngageCommentsAndProcessToES(comments, request.getChannel(), request.getPageId(), feed.getFeedId(), feed.getPageName(), request.getNewCommentId(), isFreshRequest, false);
    }


    @Override
    public EngageConversationResponse getPostCommentsDetailsV2(SocialPostDetailsCommentV2Request request, Integer accountId) {
        LOGGER.info("Request received for fetch comment details for request {}", request);
        try {
            EngageConversationResponse feed = new EngageConversationResponse();
            EngageNotificationDetails postDetails = getFeedDocumentFromEs(request.getCommentId());
            if(Objects.isNull(postDetails)) {
                LOGGER.info("No conversation found for post with request {}", request );
                return null;
            }
            if(postDetails.getIsDeleted())
            {
                LOGGER.info("The post is deleted for request {}",request);
                return new EngageConversationResponse();
            }
            SocialEngageV2 execute = engageFactory.getSocialEngageChannel(postDetails.getChannel())
                    .orElseThrow(() -> new IllegalArgumentException(INVALID_REQUEST));

            Boolean isValid = execute.isPageValid(postDetails.getPageId());
            List<EngageFeed> feeds = new ArrayList<>();
            if(request.getFetch().equalsIgnoreCase("new")) {
                    feeds = getTopCommentsFromEs(postDetails.getPostId(), postDetails.getFeedDate(), feed,isValid, accountId,postDetails.getPageId());

            } else if(request.getFetch().equalsIgnoreCase("old")) {
                feeds = getBelowCommentsFromEs(postDetails.getPostId(), postDetails.getFeedDate(), feed,isValid, accountId,postDetails.getPageId());
            }

            feed.setConversation(feeds);


            return feed;

        } catch (Exception ex) {
            LOGGER.info("Something went wrong while fetching comments for request {}", request);
            throw new BirdeyeSocialException(ex.getMessage());
        }
    }

    @Override
    public EngageConversationResponse getPostOldCommentsDetailsV2(SocialPostDetailsCommentV2Request request, Integer accountId) {
        LOGGER.info("Request received for fetch comment details for request {}", request);
        try {
            EngageConversationResponse feed = new EngageConversationResponse();
            EngageNotificationDetails postDetails = getFeedDocumentFromEs(request.getCommentId());

            if(Objects.isNull(postDetails)) {
                LOGGER.info("No conversation found for post with request {}", request );
                return null;
            }
            SocialEngageV2 execute = engageFactory.getSocialEngageChannel(postDetails.getChannel())
                    .orElseThrow(() -> new IllegalArgumentException(INVALID_REQUEST));

            Boolean isValid = execute.isPageValid(postDetails.getPageId());
            feed.setConversation(getBelowCommentsFromEs(postDetails.getEngageFeedId(), postDetails.getFeedDate(), feed,isValid, accountId,postDetails.getPageId()));

            return feed;

        } catch (Exception ex) {
            LOGGER.info("Something went wrong while fetching comments for request {}", request);
            throw new BirdeyeSocialException(ex.getMessage());
        }
    }

    private List<EngageFeed> getTopCommentsFromEs(String postId, Date targetId, EngageConversationResponse feed,boolean isValid, Integer accountId,String pageId) {
        List<EngageFeed> feedList = new ArrayList<>();
        try {
            List<EngageNotificationDetails> comments = searchPostComments(postId, pageId, targetId, null, true);
            if(CollectionUtils.isEmpty(comments)) {
                LOGGER.info(NO_PARENT_COMMENT_DOC_FOUND_LOG_MSG, postId);
                return feedList;
            }

            int nextComment = getFeedDocumentCount(comments.get(0).getFeedId(), comments.get(0).getFeedDate(), null,pageId);
            if(nextComment > 0) {
                feed.setNext(comments.get(0).getRawFeedId().toString());
            }

            List<EngageNotificationDetails> subComments = searchPostComments(postId, pageId, null, null, false);


            return convertAllCommentToSocialTimelineFeed(comments, subComments, targetId,isValid, accountId);
        } catch (Exception ex) {
            LOGGER.info(ES_INTERNAL_ERROR_LOG_MSG, postId , ex);
        }
        return feedList;
    }

    private List<EngageFeed> getBelowCommentsFromEs(String postId, Date targetId, EngageConversationResponse feed,Boolean isValid, Integer accountId,String pageId) {
        List<EngageFeed> feedList = new ArrayList<>();
        try {
            int size = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getSocialEngagePaginationCountLimit();
            List<EngageNotificationDetails> comments = searchPostComments(postId, pageId,null, targetId, true);

            if(CollectionUtils.isEmpty(comments)) {
                LOGGER.info(NO_PARENT_COMMENT_DOC_FOUND_LOG_MSG, postId);
                return feedList;
            }

            if(comments.size() == size) {
                int lastPostIndex = size - 1;
                int previousComment = getFeedDocumentCount(postId, null, comments.get(lastPostIndex).getFeedDate(),pageId);
                if(previousComment > 0) {
                    feed.setPrevious(comments.get(lastPostIndex).getRawFeedId().toString());
                }
            }

            List<EngageNotificationDetails> subComments = searchPostComments(postId, pageId, null, null, false);

            return convertBottomCommentToSocialTimelineFeed(comments, subComments, isValid, accountId);
        } catch (Exception ex) {
            LOGGER.info(ES_INTERNAL_ERROR_LOG_MSG, postId, ex);
        }
        return feedList;
    }

    private List<EngageNotificationDetails> searchPostComments(String postId, String pageId, Date from, Date to, Boolean isParentComment) {
        LOGGER.info("[searchPostComments] method called with postId: {}, pageId: {}, from: {}, to: {}, isParentComment: {}", postId, pageId, from, to, isParentComment);
        List<EngageNotificationDetails> feedDetailsList = new ArrayList<>();
        try {
            SearchRequest searchRequest = new SearchRequest(ElasticConstants.SOCIAL_ENGAGE_V2_FEED.getName());
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
            int size = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getSocialEngagePaginationCountLimit();
            size = (isParentComment != null && !isParentComment) ? 4000 : size;  // Use 4000 for sub-comments, else default size.

            BoolQueryBuilder b = new BoolQueryBuilder();
            b.must(QueryBuilders.termQuery(POST_ID_ES.concat(KEYWORD), postId));
            b.must(QueryBuilders.termQuery(IS_DELETED_ES, false));
            b.must(QueryBuilders.termQuery(PAGE_ID_ES.concat(KEYWORD), pageId));

            if (Objects.nonNull(isParentComment)) {
                b.must(QueryBuilders.termQuery(IS_PARENT_COMMENT_ES, isParentComment));
            }

            if (Objects.nonNull(from)) {
                b.must(QueryBuilders.rangeQuery(FEED_DATE).gte(DateTimeUtils.convertDateToESDateTimeUTC(from)));
            }
            if (Objects.nonNull(to)) {
                b.must(QueryBuilders.rangeQuery(FEED_DATE).lte(DateTimeUtils.convertDateToESDateTimeUTC(to)));
            }

            searchSourceBuilder.size(size);
            searchSourceBuilder.query(b);
            searchSourceBuilder.sort(FEED_DATE, SortOrder.DESC);
            searchRequest.source(searchSourceBuilder);

            SearchResponse response = esService.search(searchRequest);
            SearchHit[] searchHits = response.getHits().getHits();

            if (searchHits.length > 0) {
                for (SearchHit hit : searchHits) {
                    EngageNotificationDetails feedDetail = JSONUtils.fromJSON(hit.getSourceAsString(), EngageNotificationDetails.class);
                    feedDetailsList.add(feedDetail);
                }
            }
        } catch (Exception ex) {
            LOGGER.info(ES_FETCH_ERROR_LOG_MSG, ex);
        }
        return feedDetailsList;
    }


    private List<EngageFeed> convertBottomCommentToSocialTimelineFeed(List<EngageNotificationDetails> comments, List<EngageNotificationDetails> subComments, Boolean isValid, Integer accountId) {
        List<EngageFeed> feedList = new ArrayList<>();
        try {
            Set<Long> allTagIds = new HashSet<>();
            if(CollectionUtils.isNotEmpty(comments)) comments.forEach(comment -> {
                if(CollectionUtils.isNotEmpty(comment.getTagIds())) allTagIds.addAll(comment.getTagIds());
            });
            if(CollectionUtils.isNotEmpty(subComments)) subComments.forEach(comment -> {
                if(CollectionUtils.isNotEmpty(comment.getTagIds())) allTagIds.addAll(comment.getTagIds());
            });
            Map<Long, SocialTagBasicDetail> basicDetailMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(allTagIds)) {
                basicDetailMap = socialTagService.getTagIdsBasicDetailsFilteredOnEnterpriseId(allTagIds, accountId);
            }
            if(CollectionUtils.isEmpty(comments)) {
                LOGGER.info(PARENT_COMMENT_NOT_FOUND);
                return feedList;
            }
            Map<Long, SocialTagBasicDetail> finalBasicDetailMap = basicDetailMap;
            comments.stream().forEach(v -> {
                if(!v.getPostId().equals(v.getEventParentId()) ) {
                    return; // not a parent comment do not store it.
                }
                feedList.add(convertCommentToFeed(v, isValid, finalBasicDetailMap));
            });
            addSubComments(subComments, feedList, isValid, basicDetailMap);
            return feedList;
        } catch (Exception e) {
            LOGGER.info(INTERNAL_DATA_CONVERSION_ERROR_MSG, e);
        }
        return feedList;
    }

    private EngageFeed convertCommentToFeed(EngageNotificationDetails v, Boolean isValid, Map<Long, SocialTagBasicDetail> basicDetailMap) {
        EngageFeed feed = new EngageFeed();
        feed.setFeedText(v.getText());
        feed.setAuthorName(v.getAuthorName());
        feed.setAuthorId(v.getAuthorId());
        feed.setAuthorProfileImage(v.getAuthorProfileImage());
        feed.setAuthorUsername(v.getAuthorUsername());
        feed.setFeedId(v.getFeedId());
        feed.setRawFeedId(v.getRawFeedId());

        feed.setTargetId(v.getRawFeedId().toString());
        feed.setImageUrls(v.getImageUrls());
        feed.setVideoUrls(v.getVideoUrls());
        feed.setDatePublished(DateTimeUtils.convertDateToESDateTime(v.getFeedDate()));
        feed.setIsLikedByAdmin(v.getIsLikedByAdmin());
        feed.setFeedUrl(v.getFeedUrl());
        feed.setMarkAsComplete(v.getIsCompleted());
        feed.setIsBlocked(isUserBlocked(v.getAuthorId(), v.getPageId()));
        feed.setIsHidden(v.getIsHidden());
        feed.setPageId(v.getPageId());
        feed.setIsFollowed(isUserFollowed(v.getAuthorId(), v.getPageId(),v.getChannel()));
        feed.setFollowers(v.getFollowers());
        feed.setFollows(v.getFollows());
        feed.setIsRetweeted(v.getIsRetweeted());
        feed.setCanReplyPrivately(SocialChannel.TWITTER.name().equalsIgnoreCase(v.getChannel())? feed.getIsFollowed():v.getCanReplyPrivately());
        feed.setFeedDate(v.getFeedDate().getTime());
        feed.setIsEnglish(v.getIsEnglish());
        feed.setIsAdminComment(v.getIsAdminComment());
        feed.setIsCompleted(v.getIsCompleted());
        feed.setType(v.getType());
        feed.setSubType(v.getSubType());
        feed.setIsValid(isValid);
        feed.setReviewerUrl(v.getReviewerUrl());
        feed.setImageUrlsMetaData(v.getImageUrlsMetaData());
        feed.setVideoUrlsMetaData(v.getVideoUrlsMetaData());
        feed.setRawFeedId(v.getRawFeedId());
        feed.setLikeCount(v.getLikeCount());
        feed.setMessageTags(v.getMessageTags());
        if(COMMENT.name().equalsIgnoreCase(v.getType()) || AD_COMMENT.name().equalsIgnoreCase(v.getType())) {
            feed.setReplyCount(v.getCommentCount());
            feed.setCommentCount(null);
        }
        if(CollectionUtils.isNotEmpty(v.getTagIds()) && MapUtils.isNotEmpty(basicDetailMap)) {
            feed.setTags(basicDetailMap.values().stream()
                    .filter(s->v.getTagIds().contains(s.getId())).collect(Collectors.toList()));
        }
        return feed;
    }

    private void addSubComments(List<EngageNotificationDetails> subComments, List<EngageFeed> feedList,
                                Boolean isValid, Map<Long, SocialTagBasicDetail> basicDetailMap) {

        if(CollectionUtils.isNotEmpty(feedList) && CollectionUtils.isNotEmpty(subComments) ) {
            feedList.forEach(feed -> { //all parent comments
                List<EngageNotificationDetails> allSubComments = getNestedSubComments(feed.getFeedId(), subComments);

                if (CollectionUtils.isNotEmpty(allSubComments)) {
                    List<EngageNotificationDetails> sortedCommentsList =
                            allSubComments.stream()
                                    .sorted(Comparator.comparing(EngageNotificationDetails::getRawFeedId).reversed())
                                    .collect(Collectors.toList());

                    List<EngageFeed> conversation =
                            sortedCommentsList.stream()
                                    .map(v -> convertCommentToFeed(v, isValid, basicDetailMap))
                                    .collect(Collectors.toList());

                    feed.setReply(conversation);
                }
            });
        }
    }

    private List<EngageNotificationDetails> getNestedSubComments(String parentFeedId, List<EngageNotificationDetails> subComments) {
        List<EngageNotificationDetails> directSubComments =
                subComments.stream()
                        .filter(v -> Objects.nonNull(v.getIsParentComment()) && parentFeedId.equals(v.getEventParentId()))
                        .collect(Collectors.toList());

        // If no direct sub-comments, return an empty list
        if (directSubComments.isEmpty()) {
            return new ArrayList<>();
        }

        List<EngageNotificationDetails> allSubComments = new ArrayList<>(directSubComments);

        // Recursively fetch nested sub-comments
        directSubComments.forEach(subComment ->
                allSubComments.addAll(getNestedSubComments(subComment.getFeedId(), subComments))
        );

        return allSubComments;
    }


    @Override
    public void backfillEngageData(GenericScriptRequest scriptRequest,String channel) {
        LOGGER.info("Request received to backfill data for channel : {}", channel);
        SocialEngageV2 execute = engageFactory.getSocialEngageChannel(channel)
                .orElseThrow(() -> new IllegalArgumentException(INVALID_REQUEST));

        execute.startBackFill(scriptRequest);
    }

    @Override
    public void saveNewMessage(InboxMessageRequest inboxMessageRequest) {
        if (Objects.isNull(inboxMessageRequest)) {
            LOGGER.info("Inbox messages can not be null");
            return;
        }
        LOGGER.info("Request received to save new DM message for request {}", inboxMessageRequest);
        SocialEngageV2 execute = engageFactory.getSocialEngageChannel(inboxMessageRequest.getChannel().toLowerCase())
                .orElseThrow(() -> new IllegalArgumentException(INVALID_REQUEST));
        execute.saveMessages(inboxMessageRequest);
    }

    @Override
    public EngageFeedCountResponse newFeedCount(Integer businessId, Integer userId) {
        EngageFeedCountResponse response = new EngageFeedCountResponse();
        response.setAllChannelCount(0);

        try {
            Long enterpriseId = getEnterpriseId(businessId);
            if(Objects.isNull(enterpriseId)) {
                LOGGER.info("No enterpriseId found for businessId {}", businessId);
                return response;
            }
            String indexName = ElasticConstants.SOCIAL_ENGAGE_STATS.getName();

            FeedCounterDetails feedCounterDetails = getEngageStatsDocumentFromEs(enterpriseId);

            if(Objects.isNull(feedCounterDetails) || CollectionUtils.isEmpty(feedCounterDetails.getDetails())) {
                return response;
            }
            Map<String, Integer> channelWiseMap = new HashMap<>();
            Integer totalCount = 0;
            for(FeedUserCounterDetails v : feedCounterDetails.getDetails()) {
                if(Objects.nonNull(v.getSourceId()) &&
                        StringUtils.isNotEmpty(SocialChannel.getSocialChannelNameById(v.getSourceId()))) {
                    String channel = SocialChannel.getSocialChannelNameById(v.getSourceId());
                    if(v.getUserId().equals(userId)) {
                        channelWiseMap.put(channel, v.getCounter());
                        totalCount += v.getCounter();
                        v.setCounter(0);
                    }
                }
            }

            response.setChannelWiseCount(channelWiseMap);
            response.setAllChannelCount(totalCount);

            String doc = JSONUtils.toJSON(feedCounterDetails, JsonInclude.Include.NON_NULL);
            esService.updateDocument(doc, indexName, enterpriseId.toString());

            return response;

        } catch (Exception ex) {
            LOGGER.info("Something went while fetching new feed count for enterpriseId {}", businessId, ex);
        }
        return response;
    }

    @Override
    public void resetChannelWiseCount(Integer enterpriseId, Integer userId, String channel) {
        Long businessNumber = getEnterpriseId(enterpriseId);
        if(Objects.isNull(businessNumber)) {
            LOGGER.info("No enterpriseId found for businessId {}", enterpriseId);
            return ;
        }
        String indexName = ElasticConstants.SOCIAL_ENGAGE_STATS.getName();
        try {
            FeedCounterDetails feedCounterDetails = getEngageStatsDocumentFromEs(businessNumber);
            if(Objects.isNull(feedCounterDetails) || CollectionUtils.isEmpty(feedCounterDetails.getDetails())) {
                return;
            }
            Boolean resetAllChannel = Objects.isNull(SocialChannel.getSocialChannelByName(channel)) && "all".equals(channel);
            Integer sourceId = resetAllChannel?null:SocialChannel.getSocialChannelByName(channel).getId();
            for(FeedUserCounterDetails v : feedCounterDetails.getDetails()) {
                if(v.getUserId().equals(userId) && (resetAllChannel || (Objects.nonNull(sourceId) && sourceId.equals(v.getSourceId())))) {
                    v.setCounter(0);
                }
            }

            String doc = JSONUtils.toJSON(feedCounterDetails, JsonInclude.Include.NON_NULL);
            esService.updateDocument(doc, indexName, enterpriseId.toString());
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

    }

    @Override
    public EngageFeedRepostResponse feedRepostContentDetails(String docId) {
        EngageFeedRepostResponse response = new EngageFeedRepostResponse();

        try {
            String indexName = ElasticConstants.SOCIAL_ENGAGE_V2_FEED.getName();

            GetResponse getResponse = esService.fetchEsDocumentByDocId(docId, indexName);

            if(getResponse.isExists()) {

                response.setId(randomId());

                EngageNotificationDetails doc = JSONUtils.fromJSON(getResponse.getSourceAsString(), EngageNotificationDetails.class);
                response.setAuthorName(doc.getAuthorName());
                response.setAuthorUsername(doc.getAuthorUsername());
                response.setPostText(doc.getText());
                if(CollectionUtils.isNotEmpty(doc.getImageUrls())) {
                    List<MediaData> mediaDataList = new ArrayList<>();
                    for (MediaData media: doc.getImageUrlsMetaData()) {
                        String url =  media.getMediaUrl();
                        String metaData = media.getMediaMetaData();
                        mediaDataList.add(new MediaData(url, metaData));
                    }
                    response.setImages(mediaDataList);

                }
                if(CollectionUtils.isNotEmpty(doc.getVideoUrls())) {
                    List<MediaData> mediaDataList = new ArrayList<>();
                    for (MediaData media : doc.getVideoUrlsMetaData()) {
                        String url = media.getMediaUrl();
                        String metaData = media.getMediaMetaData();
                        mediaDataList.add(new MediaData(url, metaData));
                    }
                    response.setVideos(mediaDataList);
                }
            }
        } catch (Exception ex) {
            LOGGER.info("Something went while fetching new feed details for documentId {}", docId, ex);
        }
        return response;
    }



    private int randomId() {
       return secureRandom.nextInt();
    }

    @Override
    public void resetFeedCount(Integer businessId) {
        try {
            String indexName = ElasticConstants.SOCIAL_ENGAGE_STATS.getName();

            Long enterpriseId = getEnterpriseId(businessId);
            if(Objects.isNull(enterpriseId)) {
                LOGGER.info("No enterpriseId found for businessId {}", businessId);
                return;
            }
            esService.deleteDocument(indexName, enterpriseId.toString());

        } catch (Exception ex) {
            LOGGER.info("Something went while fetching new feed count for businessId {}", businessId, ex);
        }
    }

    private Long getEnterpriseId(Integer businessId) {
        try {
            BusinessLiteDTO businessLiteDTO = businessCoreService.getBusinessLite(businessId, false);
            if(Objects.nonNull(businessLiteDTO.getBusinessId())) {
               return businessLiteDTO.getBusinessNumber();
            }
            return null;
        } catch (Exception ex) {
            LOGGER.info("Something went wrong while fetching business details for businessId {} with {}", businessId, ex);
            return null;
        }
    }

    @Override
    public EngageFeed getPostDetails(SocialFeedDetailsRequest request, Integer accountId, Long businessNumber) {
        try {
            EngageFeed feed = new EngageFeed();
            Date targetId = null;
            EngageNotificationDetails feedDetail = getFeedDocumentFromEs(request.getRawFeedId());
            if(Objects.isNull(feedDetail)) {
                LOGGER.info("No feed found for post with request {}", request );
                return null;
            }
            LOGGER.info("Feed info: rawFeedId: {} , feedId: {}",feedDetail.getRawFeedId(), feedDetail.getFeedId());
            if((EngageV2FeedTypeEnum.COMMENT.name().equalsIgnoreCase(feedDetail.getType())
                    || AD_COMMENT.name().equalsIgnoreCase(feedDetail.getType()))
                    && Boolean.TRUE.equals(feedDetail.getIsParentComment())) {
                targetId = feedDetail.getFeedDate();
            }
            EngageNotificationDetails postDetails;
            if(EngageV2FeedTypeEnum.MESSAGE.name().equalsIgnoreCase(feedDetail.getType())) {
                postDetails=feedDetail;
            }
            else if( Objects.nonNull(feedDetail.getSourceId())
                    && feedDetail.getSourceId().equals(SocialChannel.INSTAGRAM.getId())
                    && StringUtils.isNotEmpty(feedDetail.getType())
                    && EngageV2FeedTypeEnum.COMMENT.name().equalsIgnoreCase(feedDetail.getType())) {
                Optional<EngageNotificationDetails> postDetailOpt = Optional.empty();
                List<EngageNotificationDetails> postDetailList =
                        engageConverterService.fetchEsDocByFeedId(feedDetail.getPostId());
                if(CollectionUtils.isNotEmpty(postDetailList)) {
                    postDetailOpt = postDetailList.stream().filter(
                            p -> p.getType().equalsIgnoreCase("POST")
                    ).findFirst();
                }
                if (!postDetailOpt.isPresent()) {
                    LOGGER.info("No post details found for request {}", request);
                    throw new SocialException("Could not find post details", ErrorCodes.REQUEST_ENTITY_NOT_FOUND.value());
                }
                postDetails = postDetailOpt.get();
            }
            else {
                postDetails = engageConverterService.fetchEsDocByFeedIdAndPageId(feedDetail.getPostId(), feedDetail.getPageId());
                if (Objects.isNull(postDetails)) {
                    LOGGER.info("No post details found for request {}", request);
                    throw new SocialException("Could not find post details", ErrorCodes.REQUEST_ENTITY_NOT_FOUND.value());
                }
            }
            LOGGER.info("Post info: rawFeedId: {} , feedId: {}",postDetails.getRawFeedId(), postDetails.getFeedId());
            if(Objects.nonNull(feedDetail.getIsParentComment()) && !feedDetail.getIsParentComment() &&(
                    EngageV2FeedTypeEnum.COMMENT.name().equalsIgnoreCase(feedDetail.getType())
                        || AD_COMMENT.name().equalsIgnoreCase(feedDetail.getType()))) {
                // find parent comment first.
                EngageNotificationDetails parentCommentDetails =
                        engageConverterService.fetchEsDocByFeedIdAndPageId(feedDetail.getEventParentId(), feedDetail.getPageId());
                if(Objects.nonNull(parentCommentDetails)) {
                    targetId  = parentCommentDetails.getFeedDate();
                }
            }

            Map<Long, SocialTagBasicDetail> basicDetailMap = socialTagService.getTagIdsBasicDetailsFilteredOnEnterpriseId(postDetails.getTagIds(), accountId);
            //feed.setAuthorName(postDetails.getProfileName());
            feed.setAuthorName(postDetails.getAuthorName());
            //set in case of twitter as mention data was coming wrong
            feed.setAuthorUsername(postDetails.getAuthorUsername());
            feed.setAuthorProfileImage(postDetails.getAuthorProfileImage());
            if(!ObjectUtils.isEmpty(postDetails.getFeedDate()))
                feed.setDatePublished(DateTimeUtils.convertDateToESDateTime(postDetails.getFeedDate()));
            feed.setFeedId(postDetails.getFeedId());
            feed.setRawFeedId(postDetails.getRawFeedId());
            feed.setAuthorId(postDetails.getAuthorId());
            feed.setAuthorProfileImage(postDetails.getAuthorProfileImage());
            feed.setAuthorUsername(postDetails.getAuthorUsername());
            feed.setImageUrls(postDetails.getImageUrls());
            feed.setVideoUrls(postDetails.getVideoUrls());
            feed.setImageUrlsMetaData(postDetails.getImageUrlsMetaData());
            feed.setVideoUrlsMetaData(postDetails.getVideoUrlsMetaData());
            feed.setIsLikedByAdmin(postDetails.getIsLikedByAdmin());
            feed.setCanDelete(true);
            feed.setFeedText(postDetails.getText());
            feed.setChannel(postDetails.getChannel());
            feed.setPageId(postDetails.getPageId());
            feed.setCommentCount(postDetails.getCommentCount()); // fix it
            feed.setFeedUrl(postDetails.getFeedUrl()==null? postDetails.getPostUrl() : postDetails.getFeedUrl());
            feed.setThumbnailUrl(postDetails.getThumbnailUrl());
            feed.setYtVideoUrl(postDetails.getYtVideoUrl());
            feed.setIsBlocked(isUserBlocked(postDetails.getAuthorId(), postDetails.getPageId()));
            feed.setIsHidden(postDetails.getIsHidden());
            feed.setPageName(postDetails.getPageName());
            feed.setIsFollowed(isUserFollowed(postDetails.getAuthorId(), postDetails.getPageId(),postDetails.getChannel()));
            feed.setFollowers(postDetails.getFollowers());
            feed.setFollows(postDetails.getFollows());
            feed.setIsRetweeted(postDetails.getIsRetweeted());
            feed.setCanReplyPrivately(SocialChannel.TWITTER.name().equalsIgnoreCase(postDetails.getChannel())?
                    feed.getIsFollowed():postDetails.getCanReplyPrivately());
            feed.setFeedDate(postDetails.getFeedDate().getTime());
            feed.setFeedDate(postDetails.getFeedDate().getTime());
            feed.setIsEnglish(postDetails.getIsEnglish());
            feed.setIsAdminComment(postDetails.getIsAdminComment());
            feed.setIsCompleted(postDetails.getIsCompleted());
            feed.setParentPostText(postDetails.getParentPostText());
            feed.setType(postDetails.getType());
            feed.setSubType(postDetails.getSubType());
            feed.setReviewerUrl(postDetails.getReviewerUrl());
            feed.setRawFeedId(postDetails.getRawFeedId());
            feed.setTags(MapUtils.isEmpty(basicDetailMap)? null:
                    basicDetailMap.values().stream().collect(Collectors.toList()));
            feed.setLikeCount(postDetails.getLikeCount());
            feed.setMessageTags(postDetails.getMessageTags());

            SocialEngageV2 execute = engageFactory.getSocialEngageChannel(postDetails.getChannel())
                    .orElseThrow(() -> new IllegalArgumentException(INVALID_REQUEST));

            Boolean isValid = execute.isPageValid(postDetails.getPageId());
            feed.setIsValid(isValid);

            feed.setParentPostText(postDetails.getParentPostText());
            if(SocialChannel.TWITTER.name().equalsIgnoreCase(postDetails.getChannel())) {
                feed.setSubEvent(setSubEventDetailsTwitter(postDetails.getSubEvent()));
            }

            addPageLocationName(feed,feed.getChannel());

            List<EngageFeed> comments;
            if(SocialChannel.TWITTER.name().equalsIgnoreCase(postDetails.getChannel()) &&
            postDetails.getType().equalsIgnoreCase(EngageV2FeedTypeEnum.RETWEET.name())){
                comments = getCommentsFromEs(feedDetail.getSubEvent().getFeedId(), targetId, feed, isValid, accountId,postDetails.getPageId());
            }else{
                comments = getCommentsFromEs(feedDetail.getPostId(), targetId, feed, isValid, accountId,postDetails.getPageId());
            }

            feed.setConversation(comments);

            return feed;
        } catch (SocialException e) {
            LOGGER.info("Something went wrong while fetching post details for request {} with an error {}", request, e);
            throw new SocialException(INTERNAL_ERROR_MSG, ErrorCodes.INTERNAL_SERVER_ERROR.value());
        } catch (Exception ex) {
            LOGGER.info("Something went wrong while fetching post details for request {} with an error {}", request, ex);
            throw new BirdeyeSocialException("Cannot complete the process");
        }
    }

    private EngageFeed setSubEventDetailsTwitter(EngageNotificationDetails subEvent) {
        if(Objects.isNull(subEvent)){
            return null;
        }
        EngageFeed engageFeed= new EngageFeed();
        engageFeed.setFeedText(subEvent.getText());
        engageFeed.setVideoUrls(subEvent.getVideoUrls());
        engageFeed.setVideoUrlsMetaData(subEvent.getVideoUrlsMetaData());
        engageFeed.setImageUrls(subEvent.getImageUrls());
        engageFeed.setImageUrlsMetaData(subEvent.getImageUrlsMetaData());
        if(!ObjectUtils.isEmpty(subEvent.getFeedDate()))
            engageFeed.setDatePublished(DateTimeUtils.convertDateToESDateTime(subEvent.getFeedDate()));
        engageFeed.setFeedText(subEvent.getText());
        engageFeed.setFeedUrl(subEvent.getFeedUrl());
        engageFeed.setAuthorProfileImage(subEvent.getAuthorProfileImage());
        engageFeed.setAuthorName(subEvent.getAuthorName());
        engageFeed.setAuthorUsername(subEvent.getAuthorUsername());
        engageFeed.setAuthorId(subEvent.getPageId());
        engageFeed.setFeedId(subEvent.getFeedId());
        engageFeed.setReviewerUrl(subEvent.getReviewerUrl());
        return engageFeed;

    }

    private void addPageLocationName(EngageFeed feed,String channel) {
        try {
            String pageId = feed.getPageId();
            SocialEngageV2 execute = engageFactory.getSocialEngageChannel(channel)
                    .orElseThrow(() -> new IllegalArgumentException(INVALID_REQUEST));

            EngageBusinessDetails details =  execute.getChannelEnterpriseIdByPageId(pageId);

            if(Objects.isNull(details)) {
                LOGGER.info("No business details found for pageId {}", pageId);
                return;
            }

            if(CollectionUtils.isNotEmpty(details.getBusinessIds())) {
                Integer businessId = details.getBusinessIds().get(0);

                BusinessLiteDTO businessLiteDTO =   businessCoreService.getBusinessLite(businessId, false);

                if(Objects.nonNull(businessLiteDTO)) {
                    feed.setLocation(Objects.nonNull(businessLiteDTO.getBusinessAlias()) ? businessLiteDTO.getBusinessAlias() : businessLiteDTO.getBusinessName());
                    feed.setBusinessId(businessId);
                }
            }
        } catch (Exception ex) {
            LOGGER.info("Something went wrong while fetching location details.");
        }
    }

    private boolean isUserBlocked(String authorId, String pageId) {
        List<EngageUserDetails> userDetailsList = engageUserDetailsRepo.findByAuthorIdAndPageId(authorId, pageId);
        if(CollectionUtils.isEmpty(userDetailsList)) {
            LOGGER.info("No info found for userId {} with pageId {}", authorId, pageId);
            return false;
        }
        EngageUserDetails userDetails=userDetailsList.get(0);
        return userDetails.getIsBlocked()  == 1;
    }

    @Override
    public void likePageContent(SocialEngageObjectRequest request, Integer userId, Integer accountId) throws IOException {
        EngageV2FeedSubTypeEnum engageV2FeedSubTypeEnum =
                Boolean.TRUE.equals(request.getIsLiked()) ? EngageV2FeedSubTypeEnum.LIKE : EngageV2FeedSubTypeEnum.UNLIKE;

        try {
            ExternalServiceEvent externalServiceEvent = new ExternalServiceEvent();
            SocialEngageV2 execute = engageFactory.getSocialEngageChannel(request.getChannel())
                    .orElseThrow(() -> new IllegalArgumentException(INVALID_REQUEST));


            Boolean like = request.getIsLiked();
            EngageNotificationDetails documentFromEs = null;
            if (Objects.nonNull(request.getRawFeedId())) {
                documentFromEs = getFeedDocumentFromEs(request.getRawFeedId());
            } else {
                List<EngageNotificationDetails> list = fetchEsDocByFeedId(request.getFeedId());
                documentFromEs = CollectionUtils.isNotEmpty(list) ? list.get(0) : null;
            }

            if (Objects.isNull(documentFromEs) || EngageV2FeedTypeEnum.MESSAGE_SEND.name().equalsIgnoreCase(documentFromEs.getType())) {
                LOGGER.info(NO_DOC_FOUND_LOG_MSG, request.getFeedId());
                return;
            }
            String feedId = documentFromEs.getFeedId();
            request.setFeedId(feedId);

            if (like) {
                execute.likePageContent(request, externalServiceEvent);
            } else {
                execute.unLikePageContent(request, externalServiceEvent);
            }
            if (Boolean.TRUE.equals(request.getPublishEvent())
                    && Objects.equals(EngageV2FeedTypeEnum.MESSAGE.name(), request.getType())) {
                externalServiceEvent.setIsLiked(request.getIsLiked());
                externalServiceEvent.setAccountId(accountId);
                externalServiceEvent.setFeedId(feedId);
                externalServiceEvent.setChannel(request.getChannel().toUpperCase());
                externalServiceEvent.setUserId(userId);
                externalServiceEvent.setMessengerContactId(documentFromEs.getMessengerContactId());
                kafkaProducerService.sendObjectV1(KafkaTopicEnum.SOCIAL_MESSAGE_LIKE.getName(), externalServiceEvent);
            }
            // update es doc

            documentFromEs.setIsLikedByAdmin(like);

            if(Objects.isNull(documentFromEs.getLikeCount()) || documentFromEs.getLikeCount() == 0)
                documentFromEs.setLikeCount(like ? 1 : 0);
            else
                documentFromEs.setLikeCount(like ? documentFromEs.getLikeCount() + 1 : documentFromEs.getLikeCount() - 1);

            String doc = JSONUtils.toJSON(documentFromEs, JsonInclude.Include.NON_NULL);
            esService.updateDocument(doc, ElasticConstants.SOCIAL_ENGAGE_V2_FEED.getName(), documentFromEs.getRawFeedId());
            auditEngageActions(engageV2FeedSubTypeEnum.name(), request, Constants.SUCCESS, null);
        } catch (BirdeyeSocialException e) {
            LOGGER.info("BE Error occurred while updating is liked by admin : {}", e.getLocalizedMessage());
            throw new BirdeyeSocialException(Objects.nonNull(e.getErrorCode()) ? ErrorCodes.valueOf(e.getErrorCode()) : ErrorCodes.valueOf(e.getCode())
                    , e.getMessage());
        } catch (Exception e) {
            LOGGER.info("Error occurred while updating is liked by admin : {}", e.getLocalizedMessage());
            auditEngageActions(engageV2FeedSubTypeEnum.name(), request, Constants.FAILED, e.getMessage());
            throw new SocialException(INTERNAL_ERROR_MSG, ErrorCodes.INTERNAL_SERVER_ERROR.value());
        }
    }

    @Override
    public void updateIsLiked(SocialEngageObjectRequest request) throws IOException {
        EngageV2FeedSubTypeEnum engageV2FeedSubTypeEnum = request.isLikedByAdmin() ? EngageV2FeedSubTypeEnum.LIKE : EngageV2FeedSubTypeEnum.UNLIKE;
        try {
            EngageNotificationDetails documentFromEs = getFeedDocumentFromEs(request.getRawFeedId());
            LOGGER.info("Update is like by admin for id: {}",request.getObjectId());
            if(Objects.isNull(documentFromEs)) {
                LOGGER.info("No doc found in ES to be updated for commentId :{}", request.getObjectId());
                return;
            }
            documentFromEs.setIsLikedByAdmin(request.isLikedByAdmin());
            String doc = JSONUtils.toJSON(documentFromEs, JsonInclude.Include.NON_NULL);
            esService.updateDocument(doc, ElasticConstants.SOCIAL_ENGAGE_V2_FEED.getName(), documentFromEs.getRawFeedId());
            auditEngageActions(engageV2FeedSubTypeEnum.name(), request, Constants.SUCCESS, null);
        }catch (Exception e){
            LOGGER.info("Error occurred while updating is liked by admin : {}",e.getLocalizedMessage());
            auditEngageActions(engageV2FeedSubTypeEnum.name(), request, Constants.FAILED, e.getMessage());
            throw new SocialException(INTERNAL_ERROR_MSG, ErrorCodes.INTERNAL_SERVER_ERROR.value());
        }
    }

    @Override
    public void unLikePageContent(SocialEngageObjectRequest request) throws IOException {
        LOGGER.info("Social Engage Object Request to un-like post :{}",request);
        SocialEngageV2 execute = engageFactory.getSocialEngageChannel(request.getChannel())
                .orElseThrow(() -> new IllegalArgumentException(INVALID_REQUEST));
        ExternalServiceEvent externalServiceEvent = new ExternalServiceEvent();
        execute.unLikePageContent(request,externalServiceEvent);
        request.setLikedByAdmin(false);
        updateIsLiked(request);
    }

    @Override
    public EngageFeed commentPageContent(SocialEngageObjectRequest request, Integer accountId) throws Exception {
        try {
            LOGGER.info("Comment Page content request received: {}", JSONUtils.toJSON(request));

            SocialEngageV2 engageChannel = getEngageChannel(request);
            EngageNotificationDetails document = getFeedDocumentFromEs(request.getRawFeedId());

            if(Objects.isNull(document)) {
                LOGGER.info(NO_COMMENT_DOC_FOUND_LOG_MSG, request.getRawFeedId());
                return null;
            }

            prepareRequestWithDocumentData(request, document, engageChannel);
            List<FbMessageTags> messageTags = new ArrayList<>();
            if(SocialChannel.FACEBOOK.getName().equalsIgnoreCase(request.getChannel())) {
                messageTags = createMessageTagsFromMentionData(request.getCommentMsg(), request.getMentionsData());
            }else if(SocialChannel.INSTAGRAM.getName().equalsIgnoreCase(request.getChannel()) ||
                        SocialChannel.TWITTER.getName().equalsIgnoreCase(request.getChannel())) {
                messageTags = createMessageTagsFromMentionDataForIgAndX(request.getCommentMsg(), request.getMentionsData());
            }
            request.setMessageTags(messageTags);
            if(CollectionUtils.isNotEmpty(request.getMentionsData())) {
                String text = request.getCommentMsg();
                    for (EngageCommentMentionData mentionData : request.getMentionsData()) {
                    if (text.contains(makeText(mentionData.getId()))
                            && SocialChannel.FACEBOOK.getName().equalsIgnoreCase(request.getChannel())) {
                        text = text.replace("[@" + mentionData.getId() + "]", "@["+mentionData.getId()+"]");
                    }else if (text.contains(makeText(mentionData.getMentionValue()))
                            && SocialChannel.TWITTER.getName().equalsIgnoreCase(request.getChannel())) {
                        text = text.replace("[@" + mentionData.getMentionValue() + "]","@"+mentionData.getMentionValue());
                    }else if (text.contains(makeText(mentionData.getMentionValue()))
                            && SocialChannel.INSTAGRAM.getName().equalsIgnoreCase(request.getChannel())) {
                        text = text.replace("[@" + mentionData.getMentionValue() + "]","@" +mentionData.getMentionValue());
                    }else if(text.contains(makeText(mentionData.getId())) && SocialChannel.LINKEDIN.getName().equalsIgnoreCase(request.getChannel())){
                        text = text.replace(makeText(mentionData.getMentionValue()), "@[" + mentionData.getName()+"]("+LINKEDIN_URN_PREFIX+mentionData.getId()+")");
                    }else if(text.contains(makeText(mentionData.getMentionValue())) && SocialChannel.YOUTUBE.getName().equalsIgnoreCase(request.getChannel())){
                        text = text.replace(makeText(mentionData.getMentionValue()), "@"+mentionData.getMentionValue());
                    }
                }
                request.setCommentMsg(text);
            }

            // create comment using third party API
            engageChannel.commentPageContent(request);

            if(CollectionUtils.isNotEmpty(request.getMentionsData())) {
                String text = request.getCommentMsg();
                for (EngageCommentMentionData mentionData : request.getMentionsData()) {
                    if (text.contains(makeTextInvert(mentionData.getId()))
                            && SocialChannel.FACEBOOK.getName().equalsIgnoreCase(request.getChannel())) {
                        text = text.replace("@[" + mentionData.getId() + "]", mentionData.getName());
                    }
                }
                request.setCommentMsg(text);
            }

            // update and save doc on which comment was made
            updateEngageDocument(document, request);
            saveUpdatedDocumentInEs(document);

            // update and save comment
            engageChannel.saveCommentInDbAndInES(request, document);
            engageConverterService.validateAndSaveEngageDetails(document);

            auditEngageActions(EngageActionsEnum.COMMENT.name(), request, Constants.SUCCESS, null);
            LOGGER.info("[commentPageContent] Es document entry on which comment was made: {}", document);
            updateCommentCount(document);
            return createResponse(request, document, accountId, messageTags);
        } catch (Exception ex) {
            auditEngageActions(EngageActionsEnum.COMMENT.name(), request, Constants.FAILED, ex.getMessage());
            throw ex;
        }
    }

    private SocialEngageV2 getEngageChannel(SocialEngageObjectRequest request) {
        return engageFactory.getSocialEngageChannel(request.getChannel())
                .orElseThrow(() -> new IllegalArgumentException(INVALID_REQUEST));
    }

    private void prepareRequestWithDocumentData(SocialEngageObjectRequest request, EngageNotificationDetails document, SocialEngageV2 engageChannel) {
        request.setType(document.getType());
        request.setFeedId(document.getFeedId());
        request.setPostId(document.getPostId());
        request.setIsBrandReply(true);
        String topParentCommentFeedId = null;
        if(Objects.nonNull(document.getTopParentFeedId())) {
            topParentCommentFeedId = document.getTopParentFeedId();
        } else {
            EngageNotificationDetails engageNotificationDetails = engageConverterService.findTopmostParentCommentRecursively(document.getFeedId(), document.getPageId(), document.getEventParentId());
            if(Objects.nonNull(engageNotificationDetails)) {
                topParentCommentFeedId = engageNotificationDetails.getFeedId();
            }
        }
        request.setTopParentFeedId(topParentCommentFeedId);
        document.setTopParentFeedId(topParentCommentFeedId);
        if(Objects.isNull(document.getBrandReplyId())) {
            request.setRepliedOnId(document.getFeedId());
        }

        if (SocialChannel.INSTAGRAM.getName().equalsIgnoreCase(engageChannel.channelName())) {
            request.setMediaId(document.getPostId());
            request.setSubType(document.getSubType());
        }
    }

    private void updateCommentMessageForMentions(SocialEngageObjectRequest request) {
        if (CollectionUtils.isEmpty(request.getMentionsData())) {
            return;
        }

        String text = request.getCommentMsg();
        for (EngageCommentMentionData mention : request.getMentionsData()) {
            String mentionId = mention.getId();
            String mentionValue = mention.getMentionValue();
            String mentionText = makeText(mentionId);
            String mentionValueText = makeText(mentionValue);

            switch (request.getChannel()) {
                case "FACEBOOK":
                    if (text.contains(mentionValueText)) {
                        text = text.replace("[@" + mentionValue + "]", "@[" + mentionId + "]");
                    }
                    break;
                case "TWITTER":
                case "INSTAGRAM":
                    if (text.contains(mentionText)) {
                        text = text.replace("[@" + mentionValue + "]", "@" + mention.getName());
                    }
                    break;
                case "YOUTUBE":
                    text = text.replace("[@" + mentionValue + "]", "@" + mentionValue);
                    break;
                case "LINKEDIN":
                    text = text.replace(mentionValueText, "@[" + mention.getName() + "](" + LINKEDIN_URN_PREFIX + mentionId + ")");
                    break;
            }

        }
        request.setCommentMsg(text);
    }

    private void updateEngageDocument(EngageNotificationDetails document, SocialEngageObjectRequest request) throws IOException {
        if (Boolean.TRUE.equals(request.getIsClosed())) {
            document.setIsCompleted(true);
        }
        document.setIsReplied(true);
        if (document.getBrandReplyId() == null) {
            updateParentFeedForFirstBrandReply(document, request.getCommentId(), request.getUserId(),
                    request.getFeedDate(), request.getBusinessId());
        }
    }

    private void saveUpdatedDocumentInEs(EngageNotificationDetails document) throws IOException {
        String doc = JSONUtils.toJSON(document, JsonInclude.Include.NON_NULL);
        esService.updateDocument(doc, ElasticConstants.SOCIAL_ENGAGE_V2_FEED.getName(), document.getRawFeedId());
        document.setAddedFromDashboard(true);
    }

    private void updateCommentCount(EngageNotificationDetails document) throws IOException {
        // this document is one on which comment is made.
        // if document type is "POST" then comment is made
        // if document type is "COMMENT" and subType is null then reply is made.
        // if document type is "COMMENT" and subType is "REPLY" then sub-reply is made.

        boolean isPost = EngageV2FeedTypeEnum.POST.name().equalsIgnoreCase(document.getType());      // original comment was a comment
        boolean isReply = EngageV2FeedSubTypeEnum.REPLY.name().equalsIgnoreCase(document.getSubType());  // original comment was a sub reply
        boolean isComment = !isReply && COMMENT.name().equalsIgnoreCase(document.getType());  // original comment was a reply
        boolean isInstagramOrLinkedIn = isEligibleForPostCountUpdate(document.getSourceId());

        if (isReply || isComment) {
            updateParentCommentCount(document.getTopParentFeedId(), document.getPageId(),true,1L);
            if (isInstagramOrLinkedIn) {
                updatePostCommentCount(document, true,1L, false);
            }
        } else if (isPost) {
            updatePostCommentCount(document, true, 1L, false);
        }
    }


    private boolean isEligibleForPostCountUpdate(int sourceId) {
        return sourceId == SocialChannel.LINKEDIN.getId() || sourceId == SocialChannel.INSTAGRAM.getId() || sourceId == SocialChannel.TIKTOK.getId();
    }

    private void updateParentCommentCount(String feedId, String pageId, boolean isCommentAdded, long count) throws IOException {
        LOGGER.info("Updating parent comment count with feedId: {}, pageId: {} and count: {}", feedId, pageId, count);
        EngageNotificationDetails engageNotification = engageConverterService.fetchEsDocByFeedIdAndPageId(feedId, pageId);
        updateCommentCountInternal(engageNotification, isCommentAdded, count);
    }

    private void updatePostCommentCount(EngageNotificationDetails engageNotificationDetails, boolean isCommentAdded, long count,
                                        boolean retry) throws IOException {
        LOGGER.info("Updating post comment count with postId: {}, pageId: {} and count: {}", engageNotificationDetails.getPostId(),
                engageNotificationDetails.getPageId(), count);
        EngageNotificationDetails engageNotification = engageConverterService.fetchEsDocByFeedIdAndPageId(engageNotificationDetails.getPostId(),
                engageNotificationDetails.getPageId());
        if (retry && Objects.isNull(engageNotification)) {
            LOGGER.info("Post not found for updating the comment count with postId: {}", engageNotificationDetails.getPostId());
            samayService.sendRetryRequestToSamayForContentDetails(engageNotificationDetails);
            return;
        }
        updateCommentCountInternal(engageNotification, isCommentAdded, count);
    }

    private void updateCommentCountInternal(EngageNotificationDetails engageNotification, boolean isCommentAdded, long count) throws IOException {
        if (Objects.isNull(engageNotification)) return;
        engageNotification.setCommentCount(getCommentCount(isCommentAdded, engageNotification, count));
        upsertEngageEsDocument(engageNotification);
    }

    private EngageFeed createResponse(SocialEngageObjectRequest request, EngageNotificationDetails documentFromEs, Integer accountId, List<FbMessageTags> messageTags) {
        try {
            EngageNotificationDetails commentDocumentFromEs = null;
            List<EngageNotificationDetails> commentDocumentFromEsList = fetchEsDocByFeedId(request.getCommentId());
            Map<Long, SocialTagBasicDetail> basicDetailMap = new HashMap<>();
            if(CollectionUtils.isNotEmpty(commentDocumentFromEsList)) {
                commentDocumentFromEs = commentDocumentFromEsList.get(0);
                basicDetailMap = socialTagService.getTagIdsBasicDetailsFilteredOnEnterpriseId(commentDocumentFromEs.getTagIds(), accountId);
            }
            SocialEngageV2 execute = engageFactory.getSocialEngageChannel(request.getChannel())
                    .orElseThrow(() -> new IllegalArgumentException(INVALID_REQUEST));

            EngagePageDetails engagePageDetails = execute.getRawPageDetails(documentFromEs.getPageId());

            EngageFeed response = new EngageFeed();
            response.setFeedId(request.getCommentId());
            response.setFeedText(request.getCommentMsg());
            response.setImageUrls(request.getCommentImages());
            response.setVideoUrls(request.getCommentVideos());
            response.setIsHidden(false);
            response.setAuthorName(engagePageDetails.getPageName());

            response.setAuthorId(documentFromEs.getPageId());
            if(SocialChannel.TWITTER.getName().equalsIgnoreCase(documentFromEs.getChannel())) {
                response.setAuthorProfileImage(engagePageDetails.getProfilePicUrl());
            }
            else{
                response.setAuthorProfileImage(request.getAuthorProfileImage());
            }
            response.setDatePublished(DateTimeUtils.convertDateToESDateTimeUTC(new Date()));
            response.setFeedDate(DateTimeUtils.relativeDateInMs(new Date()));
            response.setIsBlocked(false);
            response.setChannel(documentFromEs.getChannel());
            response.setIsLikedByAdmin(false);
            response.setIsAdminComment(true);
            response.setAuthorUsername(engagePageDetails.getUsername());
            response.setReviewerUrl(execute.getReviewerUrlByPageId(request.getPageId()));
            response.setCanDelete(!Objects.equals(request.getSubType(), EngageV2FeedSubTypeEnum.MENTION.name()));
            response.setRawFeedId(request.getRawFeedId());
            response.setTags(MapUtils.isEmpty(basicDetailMap)?null: new ArrayList<>(basicDetailMap.values()));
            response.setMessageTags(messageTags);
            return response;

        } catch (Exception ex) {
            LOGGER.info("Something went wrong while parsing data for it.");
            return null;
        }
    }

    private List<FbMessageTags> createMessageTagsFromMentionData(String commentMsg, List<EngageCommentMentionData> mentionsData) {
        LOGGER.info("[createMessageTagsFromMentionData] method called with commentMsg: {}, mentionData: {}", commentMsg, mentionsData);
        List<FbMessageTags> messageTags = new ArrayList<>();

        if (commentMsg != null && mentionsData != null) {
            for (EngageCommentMentionData mention : mentionsData) {
                String placeholder = "[@" + mention.getId() + "]";
                int offset = commentMsg.indexOf(placeholder);

                if (offset != -1) {
                    FbMessageTags tag = new FbMessageTags();
                    tag.setId(mention.getId());
                    tag.setName(mention.getName());
                    tag.setType("user");
                    tag.setOffset(offset);
                    tag.setLength(mention.getName().length());

                    messageTags.add(tag);
                }
            }
        }
        LOGGER.info("messageTags size : {}" , messageTags.size() );
        return messageTags;
    }

    private List<FbMessageTags> createMessageTagsFromMentionDataForIgAndX(String commentMsg, List<EngageCommentMentionData> mentionsData) {
        LOGGER.info("[createMessageTagsFromMentionData] method called with commentMsg: {}, mentionData: {}", commentMsg, mentionsData);
        List<FbMessageTags> messageTags = new ArrayList<>();
        try {
            if (StringUtils.isNotEmpty(commentMsg) && CollectionUtils.isNotEmpty(mentionsData)) {
                mentionsData = new ArrayList<>(mentionsData.stream()
                        .collect(Collectors.toMap(EngageCommentMentionData::getName, data -> data, (existing, replacement) -> existing))
                        .values());
                int backOffset = 0; // Count of previous brackets, which will be removed later.
                for (EngageCommentMentionData mention : mentionsData) {
                    String placeholder = "[@" + mention.getMentionValue() + "]";
                    int offset = commentMsg.indexOf(placeholder);
                    while (offset != -1) {
                        FbMessageTags tag = new FbMessageTags();
                        tag.setId(mention.getId());
                        tag.setName(mention.getMentionValue());
                        tag.setType("user");
                        tag.setOffset(offset - backOffset);
                        tag.setLength(mention.getMentionValue().length() + 1); // +1 for '@'
                        messageTags.add(tag);
                        backOffset += 2; // after every mention, offset shifts to left by 2, as '[' & ']' are removed
                        offset = commentMsg.indexOf(placeholder, offset + placeholder.length());
                    }
                }
            }
        }catch (Exception e){
            LOGGER.warn("Unable to create message tags from mention data for IG and X: {}", e.getMessage());
        }
        LOGGER.info("messageTags size : {}" , messageTags.size() );
        return messageTags;
    }

    private String makeTextInvert(String value) {
        value = "@["+value+"]";
        return value;
    }

    private String makeText(String value) {
        value = "[@"+value+"]";
        return value;
    }


    @Override
    public void deletePageContent(SocialEngageObjectRequest request, Integer enterpriseId, Integer userId) throws Exception {
        try {
            LOGGER.info("Received request to delete Engage card : for userId :{}, enterpriseId : {}, request:{}", userId, enterpriseId, request);
            SocialEngageV2 execute = engageFactory.getSocialEngageChannel(request.getChannel())
                    .orElseThrow(() -> new IllegalArgumentException(INVALID_REQUEST));

            // update es doc
            String feedId = request.getFeedId();
            EngageNotificationDetails documentFromEs = null;
            if(Objects.nonNull(request.getRawFeedId())){
                documentFromEs = getFeedDocumentFromEs(request.getRawFeedId());
            }else{
                List<EngageNotificationDetails> list = fetchEsDocByFeedId(request.getFeedId());
                documentFromEs = CollectionUtils.isNotEmpty(list) ? list.get(0) : null;
            }
            if(Objects.isNull(documentFromEs)) {
                LOGGER.info(NO_DOC_FOUND_LOG_MSG, feedId);
                return;
            }
            request.setPostId(documentFromEs.getPostId());
            request.setFeedId(documentFromEs.getFeedId());

            if(SocialChannel.INSTAGRAM.getName().equalsIgnoreCase(documentFromEs.getChannel())
                    && EngageV2FeedTypeEnum.POST.name().equalsIgnoreCase(documentFromEs.getType())) {
                throw new BirdeyeSocialException(ErrorCodes.INSTAGRAM_DELETE_POST_FAILED, INSTAGRAM_POST_DELETE_ERROR_MSG);
            }

            if(!Objects.equals(request.getType(),EngageActionsEnum.MESSAGE.name())) {
                execute.deletePageContent(request);
            }else{
                EngageBusinessDetails pageBusinessDetails = execute.getChannelEnterpriseIdByPageId(documentFromEs.getPageId());
                if(Objects.isNull(pageBusinessDetails) || Objects.isNull(pageBusinessDetails.getEnterpriseId()) || !Objects.equals(request.getBusinessNumber(), pageBusinessDetails.getEnterpriseId())){
                    LOGGER.info("Unauthorized access for business number {} and pageId {}", request.getBusinessNumber(), documentFromEs.getPageId());
                    throw new BirdeyeSocialException(ErrorCodes.UNAUTHORIZED_ACCESS, "You are not Authorized to perform this action");
                }
            }
            long count = 0;
            if(EngageV2FeedTypeEnum.POST.name().equalsIgnoreCase(documentFromEs.getType()) ||
                    EngageV2FeedTypeEnum.AD_POST.name().equalsIgnoreCase(documentFromEs.getType())) {
                updateIsDeletedEsDocumentByPostId(documentFromEs.getPostId(), true);
            } else if((EngageV2FeedTypeEnum.COMMENT.name().equalsIgnoreCase(documentFromEs.getType())
                    || AD_COMMENT.name().equalsIgnoreCase(documentFromEs.getType()))
                    && Objects.isNull(documentFromEs.getSubType())) {
                count = updateIsDeletedEsDocumentByEventParentId(documentFromEs.getFeedId(), documentFromEs.getIsParentComment());
            } else {
                documentFromEs.setIsDeleted(true);
                String doc = JSONUtils.toJSON(documentFromEs, JsonInclude.Include.NON_NULL);
                esService.updateDocument(doc, ElasticConstants.SOCIAL_ENGAGE_V2_FEED.getName(), documentFromEs.getRawFeedId());
            }
            if(COMMENT.name().equalsIgnoreCase(documentFromEs.getType())
                    || AD_COMMENT.name().equalsIgnoreCase(documentFromEs.getType())){
                if(count == 0) count = 1;
                updateCommentCountOnDeletion(documentFromEs, count);
            }
            // Updating deletedFeedDoc and parentCommentDoc
            handleDeleteEngageCase(documentFromEs);

            if(Boolean.TRUE.equals(request.getPublishEvent()) && Objects.equals(request.getType(),EngageActionsEnum.MESSAGE.name())){
                ExternalServiceEvent externalServiceEvent = ExternalServiceEvent.builder()
                        .source(request.getChannel())
                        .channel(request.getChannel().toUpperCase())
                        .isDeleted(true)
                        .feedId(documentFromEs.getFeedId())
                        .rawFeedId(request.getRawFeedId())
                        .userId(userId)
                        .accountId(enterpriseId)
                        .messengerContactId(documentFromEs.getMessengerContactId())
                        .build();
                kafkaProducerService.sendObjectV1(KafkaTopicEnum.SOCIAL_MESSAGE_DELETE.getName(),externalServiceEvent);
            }

            auditEngageActions(EngageActionsEnum.DELETE_CONTENT.name(), request, Constants.SUCCESS, null);
        } catch (Exception ex) {
            auditEngageActions(EngageActionsEnum.DELETE_CONTENT.name(), request, Constants.FAILED, ex.getMessage());
            throw ex;
        }
    }

    @Override
    public void markAsComplete(SocialEngageObjectRequest request) throws IOException {
        try {
            Integer feedId = request.getRawFeedId();
            EngageNotificationDetails documentFromEs = getFeedDocumentFromEs(request.getRawFeedId());

            if(Objects.isNull(documentFromEs)) {
                LOGGER.info("No doc found in ES to be updated for request {}", request);
                return;
            }
            if(Objects.nonNull(documentFromEs.getChannel()) && Objects.nonNull(documentFromEs.getPageId())){
                SocialEngageV2 execute = engageFactory.getSocialEngageChannel(documentFromEs.getChannel())
                        .orElseThrow(() -> new IllegalArgumentException(INVALID_REQUEST));
                if(Objects.nonNull(execute) ){
                    EngageBusinessDetails pageBusinessDetails = execute.getChannelEnterpriseIdByPageId(documentFromEs.getPageId());
                    if(Objects.isNull(pageBusinessDetails) || Objects.isNull(pageBusinessDetails.getEnterpriseId()) || !Objects.equals(request.getBusinessNumber(), pageBusinessDetails.getEnterpriseId())){
                        LOGGER.info("Unauthorized access for business number {} and pageId {}", request.getBusinessNumber(), documentFromEs.getPageId());
                        throw new BirdeyeSocialException(ErrorCodes.UNAUTHORIZED_ACCESS, "You are not Authorized to perform this action");
                    }
                }
            }

            documentFromEs.setIsCompleted(request.getIsCompleted());

            String doc = JSONUtils.toJSON(documentFromEs, JsonInclude.Include.NON_NULL);
            esService.updateDocument(doc, ElasticConstants.SOCIAL_ENGAGE_V2_FEED.getName(), documentFromEs.getRawFeedId());

            request.setChannel(documentFromEs.getChannel());
            auditEngageActions(EngageActionsEnum.MARK_AS_COMPLETE.name(), request, Constants.SUCCESS, null);
        } catch (Exception ex) {
            auditEngageActions(EngageActionsEnum.MARK_AS_COMPLETE.name(), request, Constants.FAILED, ex.getMessage());
            throw ex;
        }
    }

    private List<EngageFeed> getCommentsFromEs(String postId, Date targetId, EngageFeed feed,
                                               Boolean isValid, Integer accountId,String pageId) {
        List<EngageFeed> feedList = new ArrayList<>();
        try {
            int size = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getSocialEngagePaginationCountLimit();

            List<EngageNotificationDetails> comments = searchPostComments(postId, pageId , null, targetId, true); //all comments
            if(CollectionUtils.isEmpty(comments)) {
                LOGGER.info(NO_PARENT_COMMENT_DOC_FOUND_LOG_MSG, postId);
                return feedList;
            }
            List<EngageNotificationDetails> subComments = searchPostComments(postId,pageId, null, null, false); //child comments

            if(CollectionUtils.isNotEmpty(comments)) {
                int nextComment = getFeedDocumentCount(postId, comments.get(0).getFeedDate(), null,pageId);
                LOGGER.info("next comment count: {}", nextComment);
                if(nextComment > 0) {
                    feed.setNext(comments.get(0).getRawFeedId().toString());
                }

                if(comments.size() == size) {
                    int lastPostIndex = size - 1;
                    int previousComment = getFeedDocumentCount(postId, null, comments.get(lastPostIndex).getFeedDate(),pageId);
                    LOGGER.info("previous comment count: {}", previousComment);
                    if(previousComment > 0) {
                        feed.setPrevious(comments.get(lastPostIndex).getRawFeedId().toString());
                    }
                }
            }

            return convertFeedCommentToSocialTimelineFeed(comments, subComments,isValid, accountId);
        } catch (Exception ex) {
            LOGGER.info(ES_INTERNAL_ERROR_LOG_MSG, postId , ex);
        }
        return feedList;
    }



    private List<EngageFeed> convertAllCommentToSocialTimelineFeed(List<EngageNotificationDetails> comments, List<EngageNotificationDetails> subComments, Date targetId, Boolean isValid, Integer accountId) {
        List<EngageFeed> feedList = new ArrayList<>();
        try {
            Set<Long> allTagIds = new HashSet<>();
            if(CollectionUtils.isNotEmpty(comments)) comments.forEach(comment -> {
                if(CollectionUtils.isNotEmpty(comment.getTagIds())) allTagIds.addAll(comment.getTagIds());
            });
            if(CollectionUtils.isNotEmpty(subComments)) subComments.forEach(comment -> {
                if(CollectionUtils.isNotEmpty(comment.getTagIds())) allTagIds.addAll(comment.getTagIds());
            });
            Map<Long, SocialTagBasicDetail> basicDetailMap = new HashMap<>();
            if(CollectionUtils.isNotEmpty(allTagIds)) {
                basicDetailMap = socialTagService.getTagIdsBasicDetailsFilteredOnEnterpriseId(allTagIds, accountId);
            }
            if(CollectionUtils.isEmpty(comments)) {
                LOGGER.info(PARENT_COMMENT_NOT_FOUND);
                return feedList;
            }
            Map<Long, SocialTagBasicDetail> finalBasicDetailMap = basicDetailMap;
            comments.stream().forEach(v -> {
                if(Objects.isNull(v.getIsParentComment()) || !v.getIsParentComment() || v.getFeedDate().before(targetId) ) {
                    return; // not a parent comment do not store it.
                }
                feedList.add(convertCommentToFeed(v, isValid, finalBasicDetailMap));
            });
            addSubComments(subComments, feedList, isValid, basicDetailMap);
            return feedList;
        } catch (Exception e) {
            LOGGER.info(INTERNAL_DATA_CONVERSION_ERROR_MSG, e);
        }
        return feedList;
    }

    private List<EngageFeed> convertFeedCommentToSocialTimelineFeed(List<EngageNotificationDetails> comments,
                                                                    List<EngageNotificationDetails> subComments,
                                                                    Boolean isValid, Integer accountId) {
        Set<Long> allTagIds = new HashSet<>();
        if(CollectionUtils.isNotEmpty(comments)) comments.forEach(comment -> {
            if(CollectionUtils.isNotEmpty(comment.getTagIds())) allTagIds.addAll(comment.getTagIds());
        });
        if(CollectionUtils.isNotEmpty(subComments)) subComments.forEach(comment -> {
            if(CollectionUtils.isNotEmpty(comment.getTagIds())) allTagIds.addAll(comment.getTagIds());
        });
        Map<Long, SocialTagBasicDetail> basicDetailMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(allTagIds)) {
            basicDetailMap = socialTagService.getTagIdsBasicDetailsFilteredOnEnterpriseId(allTagIds, accountId);
        }
        List<EngageFeed> feedList = new ArrayList<>();
        try {
            if(CollectionUtils.isEmpty(comments)) {
                LOGGER.info(PARENT_COMMENT_NOT_FOUND);
                return feedList;
            }
            Map<Long, SocialTagBasicDetail> finalBasicDetailMap = basicDetailMap;
            comments.forEach(v -> {
                if(Objects.isNull(v.getIsParentComment()) || !v.getIsParentComment() ) {
                    return; // not a parent comment do not store it. can be removed.
                }
                feedList.add(convertCommentToFeed(v, isValid, finalBasicDetailMap));
            });
            addSubComments(subComments, feedList, isValid, basicDetailMap);
            return feedList;
        } catch (Exception e) {
            LOGGER.info(INTERNAL_DATA_CONVERSION_ERROR_MSG, e);
        }
        return feedList;
    }

    private Integer getFeedDocumentCount(String postId, Date from, Date to,String pageId) throws IOException {
        try {
            CountRequest countRequest = new CountRequest(ElasticConstants.SOCIAL_ENGAGE_V2_FEED.getName());

            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();

            BoolQueryBuilder b = new BoolQueryBuilder();
            b.must(QueryBuilders.termQuery(POST_ID_ES.concat(KEYWORD), postId));
            b.must(QueryBuilders.termQuery(PAGE_ID_ES.concat(KEYWORD), pageId));
            b.must(QueryBuilders.termQuery(IS_PARENT_COMMENT_ES, true));
            b.must(QueryBuilders.termQuery(IS_DELETED_ES, false));
            if(Objects.nonNull(from)) {
                b.must(QueryBuilders.rangeQuery(FEED_DATE).gt(DateTimeUtils.convertDateToESDateTimeUTC(from)));
            }
            if(Objects.nonNull(to)) {
                b.must(QueryBuilders.rangeQuery(FEED_DATE).lt(DateTimeUtils.convertDateToESDateTimeUTC(to)));
            }


            searchSourceBuilder.query(b);
            countRequest.source(searchSourceBuilder);

            CountResponse response = esService.countDocument(countRequest);
            return Math.toIntExact(response.getCount());

        } catch (Exception ex) {
            LOGGER.info(ES_FETCH_ERROR_LOG_MSG, ex);

        }
        return 0;
    }


    @Override
    public EngageNotificationDetails getFeedDocumentFromEs(Integer feedId) throws IOException {
       GetResponse doc = esService.fetchEsDocumentByDocId(feedId, ElasticConstants.SOCIAL_ENGAGE_V2_FEED.getName() );
       if(doc.isExists()) {
           return JSONUtils.fromJSON(doc.getSourceAsString(), EngageNotificationDetails.class);
       }
       return null;
    }
    public List<EngageNotificationDetails> fetchEsDocByFeedId(String feedId) throws IOException {
        return engageConverterService.fetchEsDocByFeedId(feedId );
    }


    private FeedCounterDetails getEngageStatsDocumentFromEs(Long enterpriseId) throws IOException {
        GetResponse doc = esService.fetchEsDocumentByDocId(enterpriseId.toString(), ElasticConstants.SOCIAL_ENGAGE_STATS.getName());
        if(doc.isExists()) {
            return JSONUtils.fromJSON(doc.getSourceAsString(), FeedCounterDetails.class);
        }
        return null;
    }

    @Override
    public void updateEsForEvent(EventUpdateRequest eventUpdateRequest) {
        try {
            LOGGER.info("Update request for request: {}",eventUpdateRequest);
            if(Objects.equals(eventUpdateRequest.getEventType(),EngageActionsEnum.BLOCK_USER)
                    || Objects.equals(eventUpdateRequest.getEventType(),EngageActionsEnum.UNBLOCK_USER)){
                blockUserEvent(eventUpdateRequest,Objects.equals(eventUpdateRequest.getEventType(),EngageActionsEnum.BLOCK_USER));
                return;
            }
            List<EngageNotificationDetails> documentFromEsList = fetchEsDocByFeedId(eventUpdateRequest.getEventId());
            if(CollectionUtils.isEmpty(documentFromEsList)) {
                LOGGER.info(NO_COMMENT_DOC_FOUND_LOG_MSG, eventUpdateRequest.getEventId());
                return;
            }
            for(EngageNotificationDetails documentFromEs : documentFromEsList) {
                switch (eventUpdateRequest.getEventType()) {
                    case MESSAGE:
                        if (Objects.nonNull(eventUpdateRequest.getStatus())
                                && Status.COMPLETE.equals(eventUpdateRequest.getStatus())) {
                            documentFromEs.setIsReplied(true);
                        }
                        if (!documentFromEs.getChannel().equalsIgnoreCase(SocialChannel.TWITTER.getName())) {
                            documentFromEs.setCanReplyPrivately(false);
                        }
                        break;
                    case DELETE_CONTENT:
                        if(!documentFromEs.getIsDeleted()) {
                            long count = updateIsDeletedEsDocumentByEventParentId(documentFromEs.getFeedId(), documentFromEs.getIsParentComment());
                            if(COMMENT.name().equalsIgnoreCase(documentFromEs.getType())
                                    || AD_COMMENT.name().equalsIgnoreCase(documentFromEs.getType())){
                                if(count == 0) count = 1;
                                updateCommentCountOnDeletion(documentFromEs, count);
                            }
                            documentFromEs.setIsDeleted(true);
                        }
                        break;
                    case LIKE:
                        if(documentFromEs.getIsLikedByAdmin()
                                && Objects.equals(documentFromEs.getPageId(),eventUpdateRequest.getFrom())){
                            LOGGER.info("Like already added for request :{}",eventUpdateRequest);
                            return;
                        }
                        documentFromEs.setLikeCount(Objects.isNull(documentFromEs.getLikeCount()) ? 1 : documentFromEs.getLikeCount() + 1);
                        if(Objects.equals(documentFromEs.getPageId(),eventUpdateRequest.getFrom()))
                            documentFromEs.setIsLikedByAdmin(true);
                        break;
                    case UNLIKE:
                        if(!documentFromEs.getIsLikedByAdmin()
                                && Objects.equals(documentFromEs.getPageId(),eventUpdateRequest.getFrom())){
                            LOGGER.info("Post is already unliked :{}",eventUpdateRequest);
                            return;
                        }
                        documentFromEs.setLikeCount(Objects.isNull(documentFromEs.getLikeCount()) ? 0 : documentFromEs.getLikeCount() - 1);
                        if(Objects.equals(documentFromEs.getPageId(),eventUpdateRequest.getFrom()))
                            documentFromEs.setIsLikedByAdmin(false);
                        break;
                    case HIDE_COMMENT:
                        documentFromEs.setIsHidden(true);
                    default:
                        documentFromEs.setIsReplied(true);
                        break;
                }
                String doc = JSONUtils.toJSON(documentFromEs, JsonInclude.Include.NON_NULL);
                esService.updateDocument(doc, ElasticConstants.SOCIAL_ENGAGE_V2_FEED.getName(), documentFromEs.getRawFeedId());
            }
        }catch (IOException ioException){
            throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST,"IO Exception occurred");
        }
    }

    private void blockUserEvent(EventUpdateRequest eventUpdateRequest, boolean isBlocked) {
        List<EngageUserDetails> userDetailsList =
                engageUserDetailsRepo.findByAuthorIdAndPageId(eventUpdateRequest.getTargetEventId(), eventUpdateRequest.getEventId());
        if(CollectionUtils.isEmpty(userDetailsList)){
            LOGGER.info("No user found with id :{} and page id :{}",eventUpdateRequest.getTargetEventId(), eventUpdateRequest.getEventId());
            return;
        }
        EngageUserDetails userDetails = userDetailsList.get(0);
        LOGGER.info("Update engage user table for user id :{} and page id :{}",eventUpdateRequest.getTargetEventId(), eventUpdateRequest.getEventId());
        userDetails.setIsBlocked(isBlocked ? 1 : 0);
        engageUserDetailsRepo.saveAndFlush(userDetails);
    }

    private void uploadEngageContentToES(EngageNotificationDetails data) throws IOException {
        String s = JSONUtils.toJSON(data);

        esService.addDocument(s, ElasticConstants.SOCIAL_ENGAGE_V2_FEED.getName(), String.valueOf(data.getRawFeedId()));
    }

    private void upsertEngageEsDocument(EngageNotificationDetails data) throws IOException {
        String s = JSONUtils.toJSON(data);
        UpdateRequest request = new UpdateRequest(ElasticConstants.SOCIAL_ENGAGE_V2_FEED.getName(), String.valueOf(data.getRawFeedId()));
        esService.upsertDocumentWithImmediateRefresh(s, request);
    }

    private void saveEngageFeedAndProcessToES(Feed feed, String source, String pageId, String pageName, String type,boolean realTimeNotification) {

        EngageNotificationDetails notificationDetails = new EngageNotificationDetails();
        LOGGER.info("saveEngageFeedAndProcessToES : Publisher details : {} - {}", feed.getPublisherName(), feed.getPublisherHandle());
        notificationDetails.setAuthorName(feed.getPublisherName());
        if(feed.getPublisherHandle() != null){
            notificationDetails.setAuthorUsername(feed.getPublisherHandle());
        }else{
            notificationDetails.setAuthorUsername(feed.getPublisherName());
        }
        notificationDetails.setAuthorProfileImage(feed.getProfileImage());
        //LOGGER.info("date in saveEngageFeedAndProcessToES :{}",feed.getDatePublished());//2023/10/23 06:45:47 yyyy-MM-dd'T'HH:mm:ss

        notificationDetails.setFeedDate(DateTimeUtils.defaultDateFormat(feed.getDatePublished()));
       // LOGGER.info("date in saveEngageFeedAndProcessToES 11 :{}",notificationDetails.getFeedDate());

        notificationDetails.setText(feed.getFeedText());
        notificationDetails.setChannel(source);
        notificationDetails.setSourceId(SocialChannel.getSocialChannelByName(source).getId());
        notificationDetails.setType(type);
        notificationDetails.setSubType(feed.getType());
        notificationDetails.setPostUrl(feed.getFeedUrl());
        notificationDetails.setFeedUrl(feed.getFeedUrl());
        // sub type will be null
        notificationDetails.setPageName(pageName);
        notificationDetails.setEngageFeedId(feed.getFeedId());
        notificationDetails.setIsCompleted(false);
        notificationDetails.setCanReplyPrivately(SocialChannel.INSTAGRAM.name().equalsIgnoreCase(source));
        LOGGER.info("Tiktok post feed image: {}", feed.getImages());
        notificationDetails.setImageUrls(feed.getImages());
        notificationDetails.setVideoUrls(feed.getVideos());
        if (SocialChannel.LINKEDIN.getName().equalsIgnoreCase(source)) {
            notificationDetails.setIsLikedByAdmin(!feed.isCan_like());
        }
        if(SocialChannel.INSTAGRAM.getName().equalsIgnoreCase(source)) {
            notificationDetails.setHideOnThread(true);
        }else{
            notificationDetails.setHideOnThread(false);
        }
        // internal parameters
        notificationDetails.setPageId(pageId);
        notificationDetails.setPostId(feed.getFeedId());
        notificationDetails.setAuthorId(feed.getPublisherId());
        notificationDetails.setReviewerUrl(feed.getProfileURL());
        notificationDetails.setFeedId(feed.getFeedId());
        notificationDetails.setMessageTags(feed.getMessageTags());
        notificationDetails.setAuthorName(feed.getPublisherName());
        notificationDetails.setSubEvent(prepareTwitterMetaData(notificationDetails,feed));
        notificationDetails.setLocationId(feed.getBusinessId());
        notificationDetails.setAccountId(feed.getAccountId());
        Integer acknowledgementId = checkAndSaveEngageFeed(notificationDetails, pageId, false);
        notificationDetails.setIsRealTimeNotification(realTimeNotification);
        if(Objects.isNull(acknowledgementId)) {
            return;
        }
        notificationDetails.setRawFeedId(acknowledgementId);
        notificationDetails.setLikeCount(feed.getLikes());
        // notificationDetails.setCommentCount(feed.getComments());
        // no need to save comment count when saving post,
        // as comment count is updated in content details API.
        kafkaProducerService.sendObjectV1(SOCIAL_ENGAGE_FEED_ES_TOPIC.getName(), notificationDetails);
    }

    private EngageNotificationDetails prepareTwitterMetaData(EngageNotificationDetails notificationDetails, Feed feed) {
        EngageNotificationDetails twitterMetaData = new EngageNotificationDetails();
        if(Objects.isNull(feed)){
            return twitterMetaData;
        }
        if(Objects.nonNull(feed.getQuotedFeed())) {
            Feed qoutedFeed = feed.getQuotedFeed();
            twitterMetaData.setAuthorName(qoutedFeed.getPublisherName());
            twitterMetaData.setAuthorId(qoutedFeed.getPublisherId());
            twitterMetaData.setPostUrl(qoutedFeed.getFeedUrl());
            twitterMetaData.setAuthorProfileImage(qoutedFeed.getProfileImage());
            twitterMetaData.setPostUrl(qoutedFeed.getFeedUrl());
            twitterMetaData.setText(qoutedFeed.getFeedText());
            twitterMetaData.setImageUrls(feed.getImages());
            twitterMetaData.setVideoUrls(feed.getVideos());
            twitterMetaData.setFeedDate(DateTimeUtils.defaultDateFormat(qoutedFeed.getDatePublished()));
            notificationDetails.setIsRetweeted(false);
            notificationDetails.setIsQuotedTweet(true);
        }
        return twitterMetaData;
    }

    private void saveEngageCommentsAndProcessToES(List<EngageNotificationDetails> comments, String source, String pageId, String postId, String pageName,
                                                  String newCommentId, boolean isFreshRequest,boolean realTimeNotification) {
        if(CollectionUtils.isEmpty(comments)) {
            LOGGER.info("No comment found for postId {}", postId);
            return;
        }

        for(EngageNotificationDetails comment: comments) {
            LOGGER.info("Comments fetched for postId {} with comment {}", postId, comment);
            comment.setPageId(pageId);
            comment.setChannel(source);
            comment.setSourceId(SocialChannel.getSocialChannelByName(source).getId());
            comment.setIsCompleted(false);
            comment.setPageName(pageName);
            // null
            comment.setType(EngageV2FeedTypeEnum.COMMENT.name());
//            // null
            if(!comment.getIsParentComment()) {
                comment.setSubType(EngageV2FeedSubTypeEnum.REPLY.name());
            }
            // use this
            if (SocialChannel.TIKTOK.getName().equals(source) || SocialChannel.FACEBOOK.getName().equals(source)) {
                comment.setHideOnThread(comment.getIsAdminComment());
            } else {
                boolean shouldShowOnThread = (isFreshRequest && !pageId.equalsIgnoreCase(comment.getAuthorId())) ||
                        (StringUtils.isNotEmpty(newCommentId) && comment.getFeedId().equalsIgnoreCase(newCommentId));
                comment.setHideOnThread(!shouldShowOnThread);
            }
            comment.setPostId(postId);

            Integer acknowledgementId = checkAndSaveEngageFeed(comment, pageId, false);
            if(Objects.isNull(acknowledgementId)) {
                continue;
            }
            comment.setRawFeedId(acknowledgementId);
            if(Objects.nonNull(newCommentId) && comment.getFeedId().equalsIgnoreCase(newCommentId))
            {
                comment.setIsRealTimeNotification(true);
            }
            else {
                comment.setIsRealTimeNotification(realTimeNotification);
            }
            String topParentFeedId = null;
            if(comment.getIsParentComment()) {
                topParentFeedId = comment.getFeedId();
            } else {
                // Check if the parent comment exists in the provided comments list and is a parent comment
                Optional<EngageNotificationDetails> parentComment = comments.stream()
                        .filter(c -> c.getFeedId().equals(comment.getEventParentId()) && c.getIsParentComment())
                        .findFirst();

                if (parentComment.isPresent()) {
                    // If parent is found and is a parent comment, use its feedId as topParentFeedId
                    topParentFeedId = parentComment.get().getFeedId();
                } else {
                    // Otherwise, check in ES
                    // if in the above condition parent comment is not fetched together then it must be already present in ES
                    // with topParentFeedId
                    EngageNotificationDetails topParentDetails = engageConverterService.findTopmostParentCommentRecursively(comment.getFeedId(), comment.getPageId(), comment.getEventParentId());
                    if (Objects.nonNull(topParentDetails)) {
                        topParentFeedId = topParentDetails.getFeedId();
                    } else {
                        LOGGER.info("Not able to find the top parent feed id for postId: {} and pageId: {}", postId, pageId);
                    }
                }
            }
            comment.setTopParentFeedId(topParentFeedId);
            kafkaProducerService.sendObjectV1(SOCIAL_ENGAGE_FEED_ES_TOPIC.getName(), comment);
        }

    }

    @Override
    public Integer checkAndSaveEngageFeed(EngageNotificationDetails details, String pageId, boolean force) {


        EngageFeedDetails engageFeedDetails = engageFeedDetailsRepo.findFirstByFeedIdAndPageId(details.getFeedId(), pageId);

        // create new entry in DB
        if(Objects.isNull(engageFeedDetails) || force) {
            EngageFeedDetails data = new EngageFeedDetails();

            data.setEngageId(details.getPostId());
            data.setText(details.getText());
            data.setPageId(details.getPageId());
            data.setFeedId(details.getFeedId());
            data.setAuthorId(details.getAuthorId());
            data.setParentId(details.getEventParentId());
            data.setType(details.getType());
            data.setFeedUrl(details.getPostUrl());
            data.setAuthorName(details.getAuthorName());
            data.setAuthorUrl(details.getReviewerUrl());
            data.setFeedDate(details.getFeedDate());
            data.setImageUrls(CollectionUtils.isNotEmpty(details.getImageUrls()) ? details.getImageUrls().toString() : null);
            data.setVideoUrls(CollectionUtils.isNotEmpty(details.getVideoUrls()) ? details.getVideoUrls().toString() : null);
            data.setAuthorProfileImage(details.getAuthorProfileImage());
            data.setAuthorId(details.getAuthorId());
            data.setSubType(details.getSubType());
            data.setChannel(details.getChannel());
            return engageFeedDetailsRepo.save(data).getId();
        }
        LOGGER.info("current post is already present in Database for feedId {}", details.getFeedId());
        return null;

    }


    @Override
    public EngageFeedResponseCountWrapper getSocialEngageFeed(EngageFeedRequest engageFeedRequest, Integer startIndex, Integer pageSize, String sortParam, String sortOrder, Integer enterpriseId) {
        LOGGER.info("Engage Request with data : {} , start index :{} , end index {}, sort param :{}",engageFeedRequest,startIndex,startIndex,sortParam);
        boolean isFacebookSelected = true;
        boolean isInstagramSelected = true;
        boolean isTwitterSelected = true;
        boolean isLinkedinSelected = true;
        boolean isYoutubeSelected = true;
        boolean isTiktokSelected = true;
        List<String> pages = new ArrayList<>();
        Map<String, Boolean> pageValidMap = new HashMap<>();
        Map<Integer, String> bizLocMapping = getBusinessLocationData(engageFeedRequest.getBusinessIds());
        Map<String, String> pageLocMapping = new HashMap<>();
        Map<String,Integer> pageBusinessIdMapping = new HashMap<>();
        Map<String, Map<String, Boolean>> validityMap = new HashMap<>();
        /** any channel/source selected **/
        if(engageFeedRequest.getMessageType() != null){
            Map<String, String> sourceMsgTypeMap = engageFeedRequest.getMessageType();
            if(sourceMsgTypeMap.get(Constants.FACEBOOK) == null){
                isFacebookSelected = false;
            }
            if(sourceMsgTypeMap.get(Constants.INSTAGRAM) == null){
                isInstagramSelected = false;
            }
            if(sourceMsgTypeMap.get(Constants.LINKEDIN) == null){
                isLinkedinSelected = false;
            }
            if(sourceMsgTypeMap.get(Constants.TWITTER) == null){
                isTwitterSelected = false;
            }
            if(sourceMsgTypeMap.get(Constants.YOUTUBE) == null){
                isYoutubeSelected = false;
            }
            if(sourceMsgTypeMap.get(Constants.TIKTOK) == null){
                isTiktokSelected = false;
            }
        }
        if(isFacebookSelected){
            List<BusinessFBPage> fbPages = socialFBPageRepository.findAllByBusinessIdIn(engageFeedRequest.getBusinessIds());
            if(CollectionUtils.isEmpty(fbPages)){
                LOGGER.info("No facebook page found for : {}", engageFeedRequest);
            }else{
                pageValidMap = facebookSocialService.getFacebookPostPermissionPageWise(fbPages, Collections.singletonList(ENGAGE));
                validityMap.put(SocialChannel.FACEBOOK.getName(),pageValidMap);
                pages.addAll(fbPages.stream().map(BusinessFBPage :: getFacebookPageId).collect(Collectors.toList()));
                fbPages.forEach(e -> {
                    pageLocMapping.put(e.getFacebookPageId(), bizLocMapping.get(e.getBusinessId()));
                    pageBusinessIdMapping.put(e.getFacebookPageId(),e.getBusinessId());
                });
            }
        }
        if(isInstagramSelected){
            List<BusinessInstagramAccount> igAccounts = businessInstagramAccountRepository.findAllByBusinessIdIn(engageFeedRequest.getBusinessIds());
            if(CollectionUtils.isEmpty(igAccounts)){
                LOGGER.info("No instagram page found for : {}", engageFeedRequest);
            }else {
                pageValidMap = instagramSocialService.getInstagramPostPermissionPageWise(igAccounts, Collections.singletonList(ENGAGE));
                validityMap.put(SocialChannel.INSTAGRAM.getName(), pageValidMap);
                pages.addAll(igAccounts.stream().map(BusinessInstagramAccount::getInstagramAccountId).collect(Collectors.toList()));
                igAccounts.forEach(e -> {
                    pageLocMapping.put(e.getInstagramAccountId(), bizLocMapping.get(e.getBusinessId()));
                    pageBusinessIdMapping.put(e.getInstagramAccountId(),e.getBusinessId());
                });
            }
        }
        if(isLinkedinSelected){
          List<BusinessLinkedinPage> linkedinPages = linkedinRepo.findAllByBusinessIdIn(engageFeedRequest.getBusinessIds());
            if(CollectionUtils.isEmpty(linkedinPages)){
                LOGGER.info("No Linkedin page found for : {}", engageFeedRequest);
            }else {
                pageValidMap = socialLinkedinService.getLinkedinPostPermissionPageWise(linkedinPages, Collections.singletonList(ENGAGE
                ));
                validityMap.put(SocialChannel.LINKEDIN.getName(), pageValidMap);
                pages.addAll(linkedinPages.stream().map(BusinessLinkedinPage::getProfileId).collect(Collectors.toList()));
                linkedinPages.forEach(e -> {
                    pageLocMapping.put(e.getProfileId(), bizLocMapping.get(e.getBusinessId()));
                    pageBusinessIdMapping.put(e.getProfileId(),e.getBusinessId());
                });
            }
        }
        if(isTwitterSelected){
            List<BusinessTwitterAccounts> twitterAccounts = socialTwitterAccountRepository.findAllByBusinessIdIn(engageFeedRequest.getBusinessIds());
            if(CollectionUtils.isEmpty(twitterAccounts)){
                LOGGER.info("No Twitter page found for : {}", engageFeedRequest);
            }else {
                pageValidMap = twitterSocialAccountService.getTwitterPostPermissionPageWise(twitterAccounts, Collections.singletonList("REPORT"));

                validityMap.put(SocialChannel.TWITTER.getName(), pageValidMap);
                pages.addAll(twitterAccounts.stream().map(account -> String.valueOf(account.getProfileId())).collect(Collectors.toList()));
                twitterAccounts.forEach(e -> {
                    pageLocMapping.put(String.valueOf(e.getProfileId()), bizLocMapping.get(e.getBusinessId()));
                    pageBusinessIdMapping.put(String.valueOf(e.getProfileId()),e.getBusinessId());
                });
            }
        }
        if(isYoutubeSelected){
            List<BusinessYoutubeChannel> youtubeChannels = youtubeChannelRepository.findAllByBusinessIdIn(engageFeedRequest.getBusinessIds());
            if(CollectionUtils.isEmpty(youtubeChannels)){
                LOGGER.info("No Youtube channel found for : {}", engageFeedRequest);
            }else {
                pageValidMap = youTubeAccountService.getYoutubePostPermissionsPageWise(youtubeChannels, Collections.singletonList("ENGAGE")); //TODO Engage permission

                validityMap.put(SocialChannel.YOUTUBE.getName(), pageValidMap);
                pages.addAll(youtubeChannels.stream().map(BusinessYoutubeChannel::getChannelId).collect(Collectors.toList()));
                youtubeChannels.forEach(e -> {
                    pageLocMapping.put(e.getChannelId(), bizLocMapping.get(e.getBusinessId()));
                });
            }
        }
        if(isTiktokSelected){
            List<BusinessTiktokAccounts> businessTiktokAccounts = businessTiktokAccountsRepository.findAllByBusinessIdIn(engageFeedRequest.getBusinessIds());
            if(CollectionUtils.isEmpty(businessTiktokAccounts)){
                LOGGER.info("No Tiktok page found for : {}", engageFeedRequest);
            } else{
                pageValidMap = tiktokAccountService.getTiktokPostPermissionPageWise(businessTiktokAccounts, Collections.singletonList(ENGAGE));
                validityMap.put(SocialChannel.TIKTOK.getName(),pageValidMap);
                pages.addAll(businessTiktokAccounts.stream().map(BusinessTiktokAccounts :: getProfileId).collect(Collectors.toList()));
                businessTiktokAccounts.forEach(e -> {
                    pageLocMapping.put(e.getProfileId(), bizLocMapping.get(e.getBusinessId()));
                    pageBusinessIdMapping.put(e.getProfileId(),e.getBusinessId());
                });
            }
        }
        //return getFeedDataFromDB(engageFeedRequest, startIndex, pageSize, sortParam, sortOrder, pages);
        return getFeedDataFromES(engageFeedRequest, startIndex, pageSize, sortParam, sortOrder, pages, pageLocMapping,pageBusinessIdMapping, validityMap, isTwitterSelected, enterpriseId);

    }

    private Map<Integer, String> getBusinessLocationData(List<Integer> businessIds) {
        Map<String, Object> businessLocationData = businessCoreService.getBusinessesInBulkByBusinessIds(businessIds, true);
        Map<Integer, String> bizLocMapping = new HashMap<>();
        for(String bizId : businessLocationData.keySet()){
            Map<String ,Object> locationData = (Map<String, Object>) businessLocationData.get(bizId);
            bizLocMapping.put(Integer.parseInt(bizId), (String) (locationData.get("businessAlias") != null ? locationData.get("businessAlias") : locationData.get("businessName")));
        }
        return bizLocMapping;
    }

    private EngageFeedResponseCountWrapper getFeedDataFromES(EngageFeedRequest engageFeedRequest, Integer startIndex,
                                                             Integer pageSize
            , String sortParam, String sortOrder, List<String> pages, Map<String, String> pageLocMapping
            , Map<String,Integer> pageBusinessIdMapping, Map<String, Map<String, Boolean>> validityMap,
                                                             boolean isTwitterSelected, Integer enterpriseId) {
        EngageFeedResponseCountWrapper countWrapper = new EngageFeedResponseCountWrapper();
        List<EngageFeedResponse> list = new ArrayList<>();
        try {
            SearchRequest searchRequest = new SearchRequest( ElasticConstants.SOCIAL_ENGAGE_V2_FEED.getName());
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();

            BoolQueryBuilder b = new BoolQueryBuilder();
            b.must(QueryBuilders.termsQuery("pageId.keyword", pages));
            b.must(QueryBuilders.termQuery(IS_DELETED_ES, false));
            b.must(QueryBuilders.termQuery("hideOnThread", false));

            if(engageFeedRequest.getSearchStr() != null && engageFeedRequest.getSearchTextType() != null) {
                if(engageFeedRequest.getSearchTextType().equalsIgnoreCase("text")){
                    b.must(QueryBuilders.matchPhraseQuery("text", engageFeedRequest.getSearchStr()));
                }else if(engageFeedRequest.getSearchTextType().equalsIgnoreCase("profile")){
                    b.must(QueryBuilders.matchPhraseQuery("authorName", engageFeedRequest.getSearchStr()));
                }

            }

            if(socialTagService.isUntaggedRequest(engageFeedRequest.getTagIds())) {
                Set<Long> tagIdsWithoutUntagged = engageFeedRequest.getTagIds().stream().filter(s->!s.equals(-1l)).collect(Collectors.toSet());
                if(CollectionUtils.isEmpty(tagIdsWithoutUntagged)) {
                    b.mustNot(QueryBuilders.existsQuery("tagIds"));
                } else {
                    BoolQueryBuilder existBoolQuery = new BoolQueryBuilder();
                    existBoolQuery.mustNot(QueryBuilders.existsQuery("tagIds"));
                    BoolQueryBuilder tagIdBoolQuery = new BoolQueryBuilder();
                    tagIdBoolQuery.should(existBoolQuery);
                    tagIdBoolQuery.should(QueryBuilders.termsQuery("tagIds", tagIdsWithoutUntagged));
                    b.must(tagIdBoolQuery);
                }
            } else if (CollectionUtils.isNotEmpty(engageFeedRequest.getTagIds())) {
                b.must(QueryBuilders.termsQuery("tagIds", engageFeedRequest.getTagIds()));
            }

//            if(engageFeedRequest.getMessageType() != null) {
//                BoolQueryBuilder pageIdTypeBoolQuery = QueryBuilders.boolQuery();
//                for(String pageId : pages){
//                    pageIdTypeBoolQuery.should(QueryBuilders.matchPhraseQuery("pageId", pageId));
//                }
//                b.should(pageIdTypeBoolQuery);
//            }

            // Note: There should not be any common field in EngageV2FeedTypeEnum and EngageV2FeedSubTypeEnum
            if(engageFeedRequest.getMessageType() != null) {
                BoolQueryBuilder channelTypeBoolQuery = QueryBuilders.boolQuery();
                Map<String, String> sourceMsgTypeMap = engageFeedRequest.getMessageType();
                for(String channel : sourceMsgTypeMap.keySet()){
                    channelTypeBoolQuery.should(QueryBuilders.boolQuery()
                            .must(QueryBuilders.termQuery("channel", channel))
                            .must(QueryBuilders.boolQuery()
                                    .should(QueryBuilders.termsQuery("type", Arrays.asList(sourceMsgTypeMap.get(channel).toLowerCase().split("\\s*,\\s*"))))
                                    .should(QueryBuilders.termsQuery("subType", Arrays.asList(sourceMsgTypeMap.get(channel).toLowerCase().split("\\s*,\\s*"))))
                            ));
                }
                b.must(channelTypeBoolQuery);
            }
            if(engageFeedRequest.getStatus() != null) {
                BoolQueryBuilder b2 = new BoolQueryBuilder();
                engageFeedRequest.getStatus().forEach(status -> {
                    if(status.equals(EngageFeedStatusEnum.Closed)){
                        b2.should(QueryBuilders.termQuery("isCompleted", true));
                    } else if(status.equals(EngageFeedStatusEnum.Open)){
                        b2.should(QueryBuilders.termQuery("isCompleted", false));
                    } else if(status.equals(EngageFeedStatusEnum.Not_Replied)){
                        b2.should(QueryBuilders.termQuery("isReplied", false));
                    }
                });
                b.must(b2);

            }
            if(engageFeedRequest.getStartDate() != null && engageFeedRequest.getEndDate() != null) {
                SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                b.must(QueryBuilders.rangeQuery(FEED_DATE).gte(DateTimeUtils.convertDateToESDateTime(format.parse(engageFeedRequest.getStartDate()))));
                b.must(QueryBuilders.rangeQuery(FEED_DATE).lte(DateTimeUtils.convertDateToESDateTime(format.parse(engageFeedRequest.getEndDate()))));
            }
            List<String> eventParentIds = new LinkedList<>();
            searchSourceBuilder.size(pageSize);
            searchSourceBuilder.from(startIndex);
            searchSourceBuilder.query(b);
            searchSourceBuilder.sort(sortParam, sortOrder.equalsIgnoreCase("ASC") ? SortOrder.ASC : SortOrder.DESC);
            searchRequest.source(searchSourceBuilder);
            SearchResponse response = esService.search(searchRequest);
            SearchHit[] searchHits = response.getHits().getHits();
            if(searchHits.length > 0) {
                List<EngageFeedEsResponse> engageFeedEsResponseList = new ArrayList<>();
                Set<Long> allTagIds = new HashSet<>();
                Set<String> messengerContactIds = new HashSet<>();
                for(SearchHit hit: searchHits) {
                    EngageFeedEsResponse engageFeedEsResponse = JSONUtils.fromJSON(hit.getSourceAsString(), EngageFeedEsResponse.class);
                    if(Objects.nonNull(engageFeedEsResponse) && CollectionUtils.isNotEmpty(engageFeedEsResponse.getTagIds())) {
                        allTagIds.addAll(engageFeedEsResponse.getTagIds());
                    }
                    if(Objects.nonNull(engageFeedEsResponse) && engageFeedEsResponse.getIsReplied())
                        eventParentIds.add(engageFeedEsResponse.getFeedId());
                    if(Objects.nonNull(engageFeedEsResponse) && Objects.nonNull(engageFeedEsResponse.getMessengerContactId())){
                        messengerContactIds.add(engageFeedEsResponse.getMessengerContactId());
                    }
                    engageFeedEsResponseList.add(engageFeedEsResponse);
                }
                Set<Integer> latestDmIds = engageConverterService.fetchLatestIncomingDmForEachConversation(messengerContactIds);
                Map<Long, SocialTagBasicDetail> basicDetailMap = getAllTagDetailsFilteredOnEnterpriseId(allTagIds, enterpriseId);
                Map<String,EngageFeedEsResponse> engageFeedEsResponses = new HashMap<>();
                if(CollectionUtils.isNotEmpty(eventParentIds)){
                    BoolQueryBuilder boolQueryBuilder = engageConverterService.createRequestToGetReplyData(eventParentIds);
                    TermsAggregationBuilder termsAggregationBuilder = engageConverterService.createAggregationForTopRecordPerEventParentId(eventParentIds.size());
                    engageFeedEsResponses = getTopReplyToCommentOrPost(boolQueryBuilder,termsAggregationBuilder);
                }
                for(EngageFeedEsResponse engageFeedEsResponse : engageFeedEsResponseList) {
                    EngageFeedResponse adPostDetails = engageConverterService.convertToEngageFeedResponse(engageFeedEsResponse);
                    if(COMMENT.name().equalsIgnoreCase(adPostDetails.getType())
                            || AD_COMMENT.name().equalsIgnoreCase(adPostDetails.getType())) {
                        adPostDetails.setReplyCount(adPostDetails.getCommentCount());
                        adPostDetails.setCommentCount(null);
                    }
                    if(MapUtils.isNotEmpty(engageFeedEsResponses) && engageFeedEsResponses.containsKey(engageFeedEsResponse.getFeedId())) {
                        EngageFeedResponse feedResponse =
                                engageConverterService.convertToEngageFeedResponse(engageFeedEsResponses.get(engageFeedEsResponse.getFeedId()));
                        adPostDetails.setResponse(feedResponse);
                    }
                    List<SocialTagBasicDetail> tags = getTagsFromTagIds(engageFeedEsResponse.getTagIds(), basicDetailMap);
                    if(CollectionUtils.isNotEmpty(tags)) {
                        adPostDetails.setTags(tags);
                    }

                    if(Objects.isNull(adPostDetails)){
                        continue;
                    }
                    adPostDetails.setIsValid(getValidFlag(validityMap,adPostDetails.getChannel(), adPostDetails.getPageId()));
                    adPostDetails.setLocation(pageLocMapping.get(adPostDetails.getPageId()));
                    adPostDetails.setBusinessId(pageBusinessIdMapping.get(adPostDetails.getPageId()));
                    adPostDetails.setCanReplyPrivately(Objects.equals(adPostDetails.getCanReplyPrivately(),true)
                            ? setCanReplyPrivately(adPostDetails.getFeedDate())
                            : adPostDetails.getCanReplyPrivately());
                    adPostDetails.setIsBlocked(isUserBlocked(adPostDetails.getAuthorId(),adPostDetails.getPageId()));
                    if(isTwitterSelected){
                        adPostDetails.setIsFollowed(isUserFollowed(adPostDetails.getAuthorId(),adPostDetails.getPageId(),adPostDetails.getChannel()));
                    }
                    // Only the latest DM in the conversation can be replied.
                    if(EngageV2FeedTypeEnum.MESSAGE.name().equals(adPostDetails.getType())){
                        adPostDetails.setCanReplyDM(latestDmIds.contains(adPostDetails.getRawFeedId()));
                    }
                    list.add(adPostDetails);
                }

                countWrapper.setCount(response.getHits().getTotalHits().value);
            }else{
                countWrapper.setCount(0L);
            }

            countWrapper.setData(list);



        } catch(IOException e){
            LOGGER.info("IO Exception while fetching data from ES with an error ", e);
            getFeedDataFromDB(engageFeedRequest, startIndex, pageSize, sortParam, sortOrder, null);
        }catch (Exception ex) {
            LOGGER.info(ES_FETCH_ERROR_LOG_MSG, ex);
        }
        return countWrapper;
    }

    private Map<String,EngageFeedEsResponse> getTopReplyToCommentOrPost(BoolQueryBuilder query,
                                                                  TermsAggregationBuilder termsAgg) {
        Map<String,EngageFeedEsResponse> engageFeedEsResponses = new HashMap<>();
        try {
            SearchSourceBuilder sourceBuilder = new SearchSourceBuilder().query(query)
                    .aggregation(termsAgg).size(0);
            SearchRequest searchRequest = new SearchRequest(ElasticConstants.SOCIAL_ENGAGE_V2_FEED.getName());
            searchRequest.source(sourceBuilder);
            SearchResponse response = esService.search(searchRequest);
            ParsedTerms topRecords = response.getAggregations().get("top_records");
            topRecords.getBuckets().forEach(bucket -> {
                String eventParentId = bucket.getKeyAsString();
                ParsedTopHits topHit = bucket.getAggregations().get("top_hit");

                for (SearchHit hit : topHit.getHits().getHits()) {
                    EngageFeedEsResponse engageFeedEsResponse = JSONUtils.fromJSON(hit.getSourceAsString(), EngageFeedEsResponse.class);
                    engageFeedEsResponses.put(eventParentId,engageFeedEsResponse);
                }
            });
        }catch (IOException e){
            throw new BirdeyeSocialException("Error Occurred while fetching data from ES",e);
        }
        return engageFeedEsResponses;
    }

    private List<SocialTagBasicDetail> getTagsFromTagIds(Set<Long> tagIds, Map<Long, SocialTagBasicDetail> basicDetailMap) {
        if(CollectionUtils.isEmpty(tagIds) || MapUtils.isEmpty(basicDetailMap)) {
            return new ArrayList<>();
        }

        return basicDetailMap.values().stream().filter(s->tagIds.contains(s.getId())).collect(Collectors.toList());
    }

    private Map<Long, SocialTagBasicDetail> getAllTagDetailsFilteredOnEnterpriseId(Set<Long> tagIds, Integer enterpriseId) {
        if(CollectionUtils.isEmpty(tagIds)) {
            return new HashMap<>();
        }
        Map<Long, SocialTagBasicDetail> basicDetailMap = socialTagService.getTagIdsBasicDetailsFilteredOnEnterpriseId(tagIds, enterpriseId);

        return basicDetailMap;
    }

    private Boolean isUserFollowed(String authorId, String pageId, String channel) {
        Integer sourceId = SocialChannel.getSocialChannelByName(channel).getId();

        List<EngageUserDetails> engageUserDetails = engageUserDetailsRepo.findByAuthorIdAndPageIdAndSourceId(authorId,
                        pageId,sourceId);
        if(CollectionUtils.isEmpty(engageUserDetails)) {
            LOGGER.info("No info found for userId {} with pageId {}", authorId, pageId);
            return false;
        }
        return engageUserDetails.get(0).getIsFollowed()  == 1;

    }

    private Boolean getValidFlag(Map<String, Map<String, Boolean>> validityMap, String channel, String pageId) {
        Map<String, Boolean> pageValidMap = validityMap.get(channel);
        if(pageValidMap != null){
            return pageValidMap.get(pageId);
        }
        return true;
    }

    private Boolean setCanReplyPrivately(Date feedDate) {
        Calendar c=Calendar.getInstance();
        c.setTime(feedDate);
        c.add(Calendar.DATE,7);
        return c.getTime().compareTo(feedDate) >= 0;
    }



    private List<EngageFeedResponse> getFeedDataFromDB(EngageFeedRequest engageFeedRequest, Integer startIndex, Integer pageSize, String sortParam, String sortOrder, List<BusinessFBPage> bizPageList){
        LOGGER.info("Fetching engage data from DB");
        List<EngageFeedResponse> list = new ArrayList<>();
        List<String> pages = bizPageList.stream().map(BusinessFBPage :: getFacebookPageId).collect(Collectors.toList());
        Map<String, Object> businessLocationData = businessCoreService.getBusinessesInBulkByBusinessIds(engageFeedRequest.getBusinessIds(), true);
        Map<String, Integer> bizPageMap = bizPageList.stream().collect(Collectors.toMap( BusinessFBPage::getFacebookPageId,
                BusinessFBPage::getBusinessId));
        boolean valid = facebookSocialService.getFacebookPostPermission(bizPageList, Collections.singletonList("REPORT"));
        try {
            PageRequest pageRequest = new PageRequest(startIndex,pageSize, sortOrder.equalsIgnoreCase("ASC") ? Sort.Direction.ASC : Sort.Direction.DESC, sortParam);
            Page<EngageFeedDetails> details = engageFeedDetailsRepo.findAll(pageRequest);
            for(EngageFeedDetails detail : details){
                EngageFeedResponse response = new EngageFeedResponse();
                response.setIsValid(valid);
                response.setEngageFeedId(detail.getEngageId());
                response.setAuthorName(detail.getAuthorName());
                response.setAuthorId(detail.getAuthorId());
                response.setFeedDate(detail.getFeedDate());
                response.setText(detail.getText());
                response.setImageUrls(Collections.singletonList(detail.getImageUrls()));
                response.setVideoUrls(Collections.singletonList(detail.getVideoUrls()));
                response.setType(detail.getType());
                response.setAuthorProfileImage(detail.getAuthorProfileImage());
                list.add(response);
            }
        }catch(Exception ex){
            LOGGER.info(ES_FETCH_ERROR_LOG_MSG, ex);
        }
        return list;
    }

    private void updateEngageSyncDate(List<BusinessYoutubeChannel> businessYoutubeChannelList) {
        List<Integer> ids = businessYoutubeChannelList.stream().map(BusinessYoutubeChannel::getId).collect(Collectors.toList());
        businessYoutubeChannelRepository.updateEngageSyncDate(ids,new Date());
    }

    @Override
    public void initYTCommentFetch() {
        LOGGER.info("Initiating YT comment fetch");
        try {
            int hours = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getYTEngageHours();
            int count = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getYTEngageAccountsCount();
            LocalDateTime localDateTime = LocalDateTime.now().minusHours(hours);
            Date date = Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
            Page<BusinessYoutubeChannel> pages = businessYoutubeChannelRepository.
                    findByIsValidAndBusinessIdNotNullAndEngageSyncDateIsLessThanEqualOrderByEngageSyncDateAsc(1, date, new PageRequest(0, count));
            if (CollectionUtils.isNotEmpty(pages.getContent())) {
                pages.getContent().forEach(businessYoutubeChannel -> {
                    YoutubeCommentRequest youtubeCommentRequest = new YoutubeCommentRequest(businessYoutubeChannel.getId());
                    kafkaProducerService.sendObject(Constants.YT_COMMENT_TOPIC, youtubeCommentRequest);
                });
                updateEngageSyncDate(pages.getContent());
            }
        } catch (Exception e) {
            LOGGER.info("Exception while initiating YT comment fetch: ",e);
        }
    }

    private void markYoutubePageInvalid(BusinessYoutubeChannel businessYoutubeChannel) {
        businessYoutubeChannel.setIsValid(0);
        businessYoutubeChannelRepository.save(businessYoutubeChannel);
        commonService.sendYoutubeSetupAuditEvent(SocialSetupAuditEnum.PAGE_DISCONNECTED.name(), Collections.singletonList(businessYoutubeChannel),
                null, businessYoutubeChannel.getBusinessId(), businessYoutubeChannel.getEnterpriseId());
    }

    private Set<String> getExistingComments(List<Item> items, Set<String> videoIds) {
        Set<String> existingCommentIds = new HashSet<>();
        List<String> commentIds = new ArrayList<>();
        items.forEach(item -> {
            commentIds.add(item.getId());
            videoIds.add(item.getSnippet().getVideoId());
            if(Objects.nonNull(item.getReplies()) && CollectionUtils.isNotEmpty(item.getReplies().getComments())) {
                List<YoutubeCommentReply.Comment> comments = item.getReplies().getComments();
                comments.forEach( comment -> commentIds.add(comment.getId()));
            }
        });
        List<EngageFeedDetails> contentList = engageFeedDetailsRepo.findAllByFeedIdIn(commentIds);
        if(CollectionUtils.isNotEmpty(contentList)) {
            existingCommentIds = contentList.stream()
                    .map(EngageFeedDetails::getFeedId)
                    .collect(Collectors.toSet());
        }
        return existingCommentIds;
    }

    private Integer saveYTDataDB(EngageNotificationDetails details) {
        EngageFeedDetails data = new EngageFeedDetails();

        data.setEngageId(details.getPostId());
        data.setText(details.getText());
        data.setPageId(details.getPageId());
        data.setFeedId(details.getFeedId());
        data.setAuthorId(details.getAuthorId());
        data.setParentId(details.getEventParentId());
        data.setType(details.getType());
        data.setFeedUrl(details.getPostUrl());
        data.setAuthorName(details.getAuthorName());
        data.setAuthorUrl(details.getReviewerUrl());
        data.setFeedDate(details.getFeedDate());
        data.setImageUrls(CollectionUtils.isNotEmpty(details.getImageUrls()) ? details.getImageUrls().toString() : null);
        data.setVideoUrls(CollectionUtils.isNotEmpty(details.getVideoUrls()) ? details.getVideoUrls().toString() : null);
        data.setAuthorProfileImage(details.getAuthorProfileImage());
        data.setAuthorId(details.getAuthorId());
        data.setChannel(details.getChannel());

        return engageFeedDetailsRepo.save(data).getId();
    }
    private void saveYTComments(YoutubeCommentSnippet snippet, String commentId, boolean isReply, BusinessYoutubeChannel businessYoutubeChannel, Integer commentCount, boolean isNewComment) {
        LOGGER.info("Saving YT comment with id: {} and payload: {}",commentId,snippet);
        EngageNotificationDetails notificationDetails = new EngageNotificationDetails();

        notificationDetails.setAuthorName(snippet.getAuthorDisplayName());
        notificationDetails.setAuthorProfileImage(snippet.getAuthorProfileImageUrl());
        notificationDetails.setFeedDate(DateTimeUtils.defaultDateFormat(snippet.getPublishedAt()));
        notificationDetails.setText(snippet.getTextOriginal());
        notificationDetails.setChannel(SocialChannel.YOUTUBE.getName());
        notificationDetails.setSourceId(SocialChannel.YOUTUBE.getId());
        notificationDetails.setType(EngageV2FeedTypeEnum.COMMENT.name());
        String parentFeedId = (isReply? snippet.getParentId(): snippet.getVideoId());
        EngageNotificationDetails parentFeed = engageConverterService.fetchEsDocByFeedIdAndPageId(parentFeedId, businessYoutubeChannel.getChannelId());
        if (Objects.nonNull(parentFeed) && Objects.equals(parentFeed.getBrandReplyId(), commentId)) {
            notificationDetails.setRepliedOnId(parentFeedId);
            notificationDetails.setIsBrandReply(true);
        }
        if(isReply) {
            notificationDetails.setSubType(EngageV2FeedSubTypeEnum.REPLY.name());
        }
        notificationDetails.setPageName(businessYoutubeChannel.getChannelName());
        notificationDetails.setAccountId(businessYoutubeChannel.getAccountId());
        notificationDetails.setLocationId(businessYoutubeChannel.getBusinessId());
        notificationDetails.setIsLikedByAdmin(false);
        notificationDetails.setEngageFeedId(snippet.getVideoId());
        notificationDetails.setIsCompleted(false);
        notificationDetails.setCanReplyPrivately(false);
        notificationDetails.setEventParentId(isReply?snippet.getParentId(): snippet.getVideoId());
        notificationDetails.setIsParentComment(!isReply);
        notificationDetails.setPostUrl("https://www.youtube.com/watch?v="+snippet.getVideoId());
        notificationDetails.setReviewerUrl(snippet.getAuthorChannelUrl());
        notificationDetails.setPageId(businessYoutubeChannel.getChannelId());
        notificationDetails.setPostId(snippet.getVideoId());
        notificationDetails.setAuthorId(snippet.getAuthorChannelId().getValue());
        notificationDetails.setFeedId(commentId);
        notificationDetails.setHideOnThread(false);
        notificationDetails.setIsAdminComment(businessYoutubeChannel.getChannelId().equalsIgnoreCase(notificationDetails.getAuthorId()));
        Integer acknowledgementId = saveYTDataDB(notificationDetails);
        notificationDetails.setRawFeedId(acknowledgementId);
        notificationDetails.setIsRealTimeNotification(false);
        notificationDetails.setLikeCount(snippet.getLikeCount());
        notificationDetails.setCommentCount(commentCount);
        if(isNewComment) {
            kafkaProducerService.sendObjectV1(SOCIAL_ENGAGE_FEED_ES_TOPIC.getName(), notificationDetails);
        }else{
            // Comment already exist, updating its like/comment count particularly
            kafkaProducerService.sendObjectV1(SOCIAL_ENGAGE_FEED_ES_TOPIC_UPDATE.getName(), notificationDetails);
        }
    }

    public void saveYTVideos(BusinessYoutubeChannel youtubeChannel, Set<String> videoIds) {
        List<String> videoList = new ArrayList<>(videoIds);
        List<EngageFeedDetails> contentList = engageFeedDetailsRepo.findAllByFeedIdIn(videoList);
        Set<String> existingVideoIds = contentList.stream()
                .map(EngageFeedDetails::getFeedId)
                .collect(Collectors.toSet());
        LOGGER.info("Existing video ids: {}",existingVideoIds);
        List<String> newVideoIds = videoIds.stream()
                .filter(videoId -> !existingVideoIds.contains(videoId))
                .collect(Collectors.toList());
        List<String> storedVideoIds = new ArrayList<>(existingVideoIds);

        int batchSize = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getYTEnagageIdsBatchSize();

        List<List<String>> batchedVideoIds = IntStream.range(0, (newVideoIds.size() + batchSize - 1) / batchSize)
                .mapToObj(i -> newVideoIds.subList(i * batchSize, Math.min(newVideoIds.size(), (i + 1) * batchSize)))
                .collect(Collectors.toList());

        List<List<String>> batchedExistingVideoIds = IntStream.range(0, (storedVideoIds.size() + batchSize - 1) / batchSize)
                .mapToObj(i -> storedVideoIds.subList(i * batchSize, Math.min(storedVideoIds.size(), (i + 1) * batchSize)))
                .collect(Collectors.toList());

        Integer refreshTokenId = youtubeChannel.getRefreshTokenId();
        String key = youtubeChannel.getId()+"-"+refreshTokenId;
        for(List<String> videoIdList : batchedVideoIds) {
            LOGGER.info("Saving video info for video ids list size : {}",videoIdList.size());
            // push to kafka in batches
            YoutubeSaveVideoInfoDTO request = YoutubeSaveVideoInfoDTO.builder()
                    .refreshTokenId(refreshTokenId)
                    .videoIdList(videoIdList)
                    .channelId(youtubeChannel.getChannelId())
                    .channelName(youtubeChannel.getChannelName())
                    .pictureUrl(youtubeChannel.getPictureUrl())
                    .accountId(youtubeChannel.getAccountId())
                    .locationId(youtubeChannel.getBusinessId())
                    .isNewVideo(true)
                    .build();
            kafkaProducerService.sendObjectWithKeyV1(key, Constants.YT_SAVE_VIDEO_INFO_TOPIC, request);
        }
        for(List<String> videoIdList : batchedExistingVideoIds) {
            LOGGER.info("Updating video engage info for video ids list size : {}",videoIdList.size());
            // push to kafka in batches
            YoutubeSaveVideoInfoDTO request = YoutubeSaveVideoInfoDTO.builder()
                    .refreshTokenId(refreshTokenId)
                    .videoIdList(videoIdList)
                    .channelId(youtubeChannel.getChannelId())
                    .channelName(youtubeChannel.getChannelName())
                    .pictureUrl(youtubeChannel.getPictureUrl())
                    .isNewVideo(false) // Updating Existing Video
                    .build();
            kafkaProducerService.sendObjectWithKeyV1(key, Constants.YT_SAVE_VIDEO_INFO_TOPIC, request);
        }
    }
    @Override
    public void saveYTVideoInfo(YoutubeSaveVideoInfoDTO request){
        LOGGER.info("Request received to save YT video info for payload: {}", request);
        Object googleAuthTokenObject = cacheService.get("ytRefreshTokenCache", ""+request.getRefreshTokenId());
        GoogleAuthToken googleAuthToken = Objects.nonNull(googleAuthTokenObject) ? (GoogleAuthToken) googleAuthTokenObject :
                googleAuthenticationService.getYoutubeAuthTokensCached(request.getRefreshTokenId());
        if(Objects.isNull(googleAuthToken) || Objects.isNull(googleAuthToken.getAccess_token())){
            LOGGER.info("Unable to get access token for refresh token Id : {}", request.getRefreshTokenId());
            return;
        }
        if(request.getIsNewVideo()) {
            saveYTVideosInBatches(request, googleAuthToken.getAccess_token());
        }else{
            updateYTVideosInBatches(request, googleAuthToken.getAccess_token());
        }
    }

    public void saveYTVideosInBatches(YoutubeSaveVideoInfoDTO request, String accessToken){
        YoutubeCommentThreadResponse response = null;
        try{
            response = youtubeService.getVideoInfo(accessToken,request.getVideoIdList());
            LOGGER.info("Youtube video info response: {}",response);
        }catch (Exception e){
            LOGGER.info("Exception while fetching video info for video ids: {}",request.getVideoIdList());
            return;
        }
        if(Objects.nonNull(response) && CollectionUtils.isNotEmpty(response.getItems())) {
            response.getItems().forEach(item -> {
                LOGGER.info("Saving video info for item: {}",item);
                EngageNotificationDetails notificationDetails = new EngageNotificationDetails();

                notificationDetails.setAuthorName(item.getSnippet().getChannelTitle());
                notificationDetails.setAuthorProfileImage(request.getPictureUrl());
                notificationDetails.setFeedDate(DateTimeUtils.defaultDateFormat(item.getSnippet().getPublishedAt()));
                notificationDetails.setText(item.getSnippet().getTitle());
                notificationDetails.setChannel(SocialChannel.YOUTUBE.getName());
                notificationDetails.setSourceId(SocialChannel.YOUTUBE.getId());
                notificationDetails.setType(EngageV2FeedTypeEnum.POST.name());
                notificationDetails.setPageName(request.getChannelName());
                notificationDetails.setIsLikedByAdmin(false);
                notificationDetails.setIsParentComment(false);
                notificationDetails.setEngageFeedId(item.getId());
                notificationDetails.setIsCompleted(false);
                notificationDetails.setCanReplyPrivately(false);
                notificationDetails.setEventParentId(null);
                notificationDetails.setPostUrl("https://www.youtube.com/watch?v="+item.getId());
                notificationDetails.setFeedUrl("https://www.youtube.com/watch?v="+item.getId());
                notificationDetails.setReviewerUrl("http://www.youtube.com/channel/"+item.getSnippet().getChannelId());
                notificationDetails.setYtVideoUrl("https://www.youtube.com/embed/"+item.getId());
                notificationDetails.setPageId(request.getChannelId());
                notificationDetails.setPostId(item.getId());
                notificationDetails.setAuthorId(item.getSnippet().getChannelId());
                notificationDetails.setFeedId(item.getId());
                notificationDetails.setHideOnThread(true);
                notificationDetails.setThumbnailUrl(item.getSnippet().getThumbnails().getMedium().getUrl());
                //flag for realTime event, for Engage Email Alert
                notificationDetails.setIsRealTimeNotification(false);
                notificationDetails.setAccountId(request.getAccountId());
                notificationDetails.setLocationId(request.getLocationId());

                Integer acknowledgementId = saveYTDataDB(notificationDetails);
                notificationDetails.setRawFeedId(acknowledgementId);
                if(Objects.nonNull(item.getStatistics().getLikeCount())) {
                    notificationDetails.setLikeCount(Integer.parseInt(item.getStatistics().getLikeCount()));
                }
                if(Objects.nonNull(item.getStatistics().getCommentCount())) {
                    notificationDetails.setCommentCount(Integer.parseInt(item.getStatistics().getCommentCount()));
                }

                kafkaProducerService.sendObjectV1(SOCIAL_ENGAGE_FEED_ES_TOPIC.getName(), notificationDetails);
            });
        }
    }
    public void updateYTVideosInBatches(YoutubeSaveVideoInfoDTO request, String accessToken){
        YoutubeCommentThreadResponse response = null;
        try{
            response = youtubeService.getVideoInfo(accessToken,request.getVideoIdList());
            LOGGER.info("Youtube video info response: {}",response);
        }catch (Exception e){
            LOGGER.info("Exception while fetching video info for video ids: {}",request.getVideoIdList());
            return;
        }
        if(Objects.nonNull(response) && CollectionUtils.isNotEmpty(response.getItems())) {
            response.getItems().forEach(item -> {
                LOGGER.info("Saving video info for item: {}",item);
                EngageNotificationDetails notificationDetails = new EngageNotificationDetails();
                notificationDetails.setFeedId(item.getId());
                if(Objects.nonNull(item.getStatistics().getLikeCount())) {
                    notificationDetails.setLikeCount(Integer.parseInt(item.getStatistics().getLikeCount()));
                }
                if(Objects.nonNull(item.getStatistics().getCommentCount())) {
                    notificationDetails.setCommentCount(Integer.parseInt(item.getStatistics().getCommentCount()));
                }
                kafkaProducerService.sendObjectV1(SOCIAL_ENGAGE_FEED_ES_TOPIC_UPDATE.getName(), notificationDetails);
            });
        }
    }
    private void auditYTComments(String channelId, Integer commentCount, Integer YT_ENGAGE_LIMIT) {
        YoutubeEngageLimit youtubeEngageLimit = new YoutubeEngageLimit();
        youtubeEngageLimit.setChannelId(channelId);
        youtubeEngageLimit.setCommentCount(commentCount);
        youtubeEngageLimit.setLimitReached(commentCount>YT_ENGAGE_LIMIT?1:0);
        youtubeEngageLimit.setCreated(new Date());
        youtubeEngageLimitRepo.saveAndFlush(youtubeEngageLimit);
    }

    private void processYTData(List<Item> items, BusinessYoutubeChannel businessYoutubeChannel) throws Exception {
        if(CollectionUtils.isNotEmpty(items)) {
            Set<String> videoIds = new HashSet<>();
            Set<String> existingCommentIds = getExistingComments(items,videoIds);
            LOGGER.info("Existing comment ids: {}",existingCommentIds);
            items.forEach(item -> {
                saveYTComments(item.getSnippet().getTopLevelComment().getSnippet(), item.getId(),false,
                        businessYoutubeChannel, item.getSnippet().getTotalReplyCount(),
                        !existingCommentIds.contains(item.getId()));

                if(Objects.nonNull(item.getReplies()) && CollectionUtils.isNotEmpty(item.getReplies().getComments())) {
                    List<YoutubeCommentReply.Comment> comments = item.getReplies().getComments();
                    comments.forEach( comment ->
                            saveYTComments(comment.getSnippet(), comment.getId(),true, businessYoutubeChannel, null,
                                    !existingCommentIds.contains(comment.getId()))
                    );
                }
            });
            saveYTVideos(businessYoutubeChannel,videoIds);
        }
    }

    @Override
    public void getYTComments(YoutubeCommentRequest youtubeCommentRequest) {
        LOGGER.info("Request received to get youtube comments for payload: {}",youtubeCommentRequest);
        int ytEngageLimit = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getYTEngageLimit();

        if(Objects.isNull(youtubeCommentRequest) || Objects.isNull(youtubeCommentRequest.getId())) {
            return;
        }
        Integer id= youtubeCommentRequest.getId();
        BusinessYoutubeChannel businessYoutubeChannel = businessYoutubeChannelRepository.findById(id);
        if(Objects.nonNull(businessYoutubeChannel) && businessYoutubeChannel.getIsValid() == 1) {
            GoogleAuthToken googleAuthToken;
            try {
                googleAuthToken = googleAuthenticationService.getYoutubeAuthTokensCached(businessYoutubeChannel.getRefreshTokenId());
                if (Objects.isNull(googleAuthToken)) {
                    LOGGER.info("Unable to get access token from refresh token for YT id {}", id);
                    return ;
                }
                List<Item> items = new ArrayList<>();
                String nextToken = null;
                int totalCount=0;
                do {
                    YoutubeCommentThreadResponse response = youtubeService.getYTComments(googleAuthToken.getAccess_token(), businessYoutubeChannel.getChannelId(),nextToken);
                    nextToken = response.getNextPageToken();
                    items.addAll(response.getItems());
                    totalCount+=response.getPageInfo().getTotalResults();
                    LOGGER.info("Response: {}", response);
                } while(StringUtils.isNotEmpty(nextToken) && (totalCount<ytEngageLimit));
                auditYTComments(businessYoutubeChannel.getChannelId(),totalCount,ytEngageLimit);
                processYTData(items,businessYoutubeChannel);
            }catch (BirdeyeSocialException e){
                LOGGER.info("BirdEyeSocialException while fetching youtube channel comments : {}", e.getMessage());
                if(Objects.nonNull(e.getCode()) && e.getCode() == ErrorCodes.UNAUTHORIZED_OR_EXPIRED_TOKEN.value()) {
                    markYoutubePageInvalid(businessYoutubeChannel);
                    LOGGER.info("Marked youtube page invalid: {}",businessYoutubeChannel.getId());
                }
            } catch (Exception e) {
                LOGGER.info("Exception while getting youtube comments : ",e);
            }
        }
    }

    @Override
    public void getFeedEngage(String channel, PostData postData){
        if(Objects.isNull(channel) || Objects.isNull(postData)){
            LOGGER.info("[ENGAGE SYNC] Invalid request for channel {} and pageId {}", channel, postData);
            return;
        }
        LOGGER.info("[ENGAGE SYNC] Request received for postId {} and pageId {}", postData.getPostId(), postData.getPageId());
        SocialEngageV2 execute = engageFactory.getSocialEngageChannel(channel)
                .orElseThrow(() -> new IllegalArgumentException(INVALID_REQUEST));

        EngageNotificationDetails postFeed = EngageNotificationDetails.builder()
                .feedId(postData.getPostId())
                .likeCount(postData.getLikeCount())
                .commentCount(postData.getCommentCount())
                .build();
        kafkaProducerService.sendObjectV1(SOCIAL_ENGAGE_FEED_ES_TOPIC_UPDATE.getName(), postFeed);

        if("story".equals(postData.getPostType())) {
            LOGGER.info("Comments are not applicable for IG story post");
            return;
        }
        EngageCommentRequest engageCommentRequest = new EngageCommentRequest();
        engageCommentRequest.setObjectId(postFeed.getFeedId());
        engageCommentRequest.setPageId(postData.getPageId());
        // fetch all comments on the post
        List<EngageNotificationDetails> comments = execute.getCommentData(engageCommentRequest);
        LOGGER.info("[ENGAGE SYNC] Comments fetched for postId {} and count : {}", postData.getPostId(), CollectionUtils.size(comments));
        for(EngageNotificationDetails commentFeed: comments){
            kafkaProducerService.sendObjectV1(SOCIAL_ENGAGE_FEED_ES_TOPIC_UPDATE.getName(), commentFeed);
        }

    }

    @Override
    public void webhookSubscriptionRequest(EngageWebhookSubscriptionRequest request) {
        try {
            SocialEngageV2 execute = engageFactory.getSocialEngageChannel(request.getChannel())
                    .orElseThrow(() -> new IllegalArgumentException(INVALID_REQUEST));

            if(request.getSubscription()) {
                Boolean syncRequired = execute.subscribeNotificationWebhook(request.getPageId());
                if(syncRequired && request.getBackFill()) {
                    kafkaProducerService.sendObject(FBNotificationKafkaEventNameEnum.SOCIAL_ENGAGE_FEED_SYNC_REQUEST.getName(), request);
                }
            } else {
                execute.unSubscribeNotificationWebhook(request.getPageId());
            }
        } catch (Exception ex) {
            LOGGER.info("Something went wrong while subscription handler for request {} with error", request, ex );
        }
    }


    public void insertNewNotificationInFirebase(Integer enterpriseId, Integer businessId) {
        LOGGER.info("Event sent for new notification to firebase for enterpriseId: {} and businessId: {}", enterpriseId, businessId);
        nexusService.insertMapInFirebase(FireBaseConstants.getSocialEngageNewNotificationTopic(enterpriseId),
                businessId+"" , System.currentTimeMillis()+"");
    }


    @Override
    public void syncEsWithDb() {
        int page = 0;
        int size = 500;
        List<String> engageNotificationDetails;

        while (true) {
            engageNotificationDetails = engageFeedDetailsRepo.getMetaDataForAll(new PageRequest(page++, size));
            if (CollectionUtils.isEmpty(engageNotificationDetails)) {
                break;
            }

            engageNotificationDetails.forEach(efd -> {
                if (StringUtils.isNotEmpty(efd)) {
                    EngageNotificationDetails engageNotificationDetails1=JSONUtils.fromJSON(efd, EngageNotificationDetails.class);
                    if(Objects.nonNull(engageNotificationDetails1)) {
                        engageNotificationDetails1.setIsRealTimeNotification(false);
                        kafkaProducerService.sendObjectV1(SOCIAL_ENGAGE_FEED_ES_TOPIC.getName(),
                                engageNotificationDetails1);
                    }
                }
            });

        }
    }

    public void updateIsDeletedForChildComments(String feedId) {
        LOGGER.info("request received to delete child comments for feed id {}", feedId);

        try {
            UpdateByQueryRequest updateRequest = new UpdateByQueryRequest(ElasticConstants.SOCIAL_ENGAGE_V2_FEED.getName());
            updateRequest.setQuery(QueryBuilders.matchQuery(EVENT_PARENT_ID_ES.concat(KEYWORD), feedId));

            Map<String, Object> data = new HashMap<>();
            data.put(VALUE, true);
            Script inline = new Script(ScriptType.INLINE, PAINLESS, SOURCE_DELETE, data);
            updateRequest.setScript(inline);
            LOGGER.info(QUERY_DOC_LOG_MSG , updateRequest);

            BulkByScrollResponse response = esService.updateByQueryRequest(updateRequest);

            long updatedDocuments = response.getUpdated();
            LOGGER.info(UPDATED_DOC_LOG_MSG , updatedDocuments);


        } catch (Exception ex) {
            LOGGER.info("No child comment found to be updated for feed id {}", feedId);
        }
    }

    private void auditEngageActions(String action, SocialEngageObjectRequest request, String status, String failureMessage) {
        LOGGER.info("Auditing engage actions for action: {}, status: {}, request: {}", action, status, request);
        if (Objects.nonNull(action)) {
            kafkaProducerService.sendObjectV1(Constants.SOCIAL_ENGAGE_ACTIONS_AUDIT_TOPIC,
                    new EngageActionAudit(action, request.getChannel(), request.getPageId(), request.getObjectId(),
                            status, failureMessage));
        }
    }

    @Override
    public void followUser(SocialEngageObjectRequest request) {
        try {
            SocialEngageV2 execute = engageFactory.getSocialEngageChannel(request.getChannel())
                    .orElseThrow(() -> new IllegalArgumentException(INVALID_REQUEST));

            Integer sourceId = SocialChannel.getSocialChannelByName(request.getChannel()).getId();

            execute.followUser(request);
            if(Boolean.TRUE.equals(request.getIsFollowed())) {
                execute.followUser(request);
            } else {
                execute.unfollowUser(request);
            }

            // update SQL entry
            List<EngageUserDetails> userDetailsList = engageUserDetailsRepo.findByAuthorIdAndPageIdAndSourceId(request.getAuthorId(),
                    request.getPageId(),sourceId);
            if(CollectionUtils.isEmpty(userDetailsList)) {
                LOGGER.info("No entry found for engage user for request {}", request);
                return;
            }
            EngageUserDetails userDetails = userDetailsList.get(0);
            if(Boolean.TRUE.equals(request.getIsFollowed())) {
                userDetails.setIsFollowed(1);
                auditEngageActions(EngageActionsEnum.FOLLOW_USER.name(), request, Constants.SUCCESS, null);
            } else {
                userDetails.setIsFollowed(0);
                auditEngageActions(EngageActionsEnum.UNFOLLOW_USER.name(), request, Constants.SUCCESS, null);
            }

            engageUserDetailsRepo.saveAndFlush(userDetails);
        } catch (Exception ex) {
            auditEngageActions(Boolean.TRUE.equals(request.getIsFollowed())?EngageActionsEnum.FOLLOW_USER.name():EngageActionsEnum.UNFOLLOW_USER.name(),
                    request,Constants.FAILED, ex.getMessage());
            throw ex;
        }
    }

    @Override
    public void retweetData(SocialEngageObjectRequest request) throws IOException {
        String action = null;
        try {

            SocialEngageV2 execute = engageFactory.getSocialEngageChannel(request.getChannel())
                    .orElseThrow(() -> new IllegalArgumentException(INVALID_REQUEST));
            Integer objectId = request.getRawFeedId();
            EngageNotificationDetails documentFromEs = getFeedDocumentFromEs(objectId);
            if(Objects.isNull(documentFromEs)) {
                LOGGER.info("No doc found in ES to be updated for tweetId {}", objectId);
                return;
            }
            request.setFeedId(documentFromEs.getFeedId());
            if(Boolean.TRUE.equals(request.getIsRetweeted())) {
                execute.shareComment(request);
            } else {
                execute.unShareComment(request);
            }
            //String objectId = request.getFeedId();


            if (Boolean.TRUE.equals(request.getIsRetweeted())) {
                action= EngageActionsEnum.RETWEET.name();
                // update es doc
                documentFromEs.setIsRetweeted(true);

            }
            else{
                action= EngageActionsEnum.UNDO_REWTEET.name();
                // update es doc
                documentFromEs.setIsRetweeted(false);
            }
            String doc = JSONUtils.toJSON(documentFromEs, JsonInclude.Include.NON_NULL);
            esService.updateDocument(doc, ElasticConstants.SOCIAL_ENGAGE_V2_FEED.getName(), documentFromEs.getRawFeedId());
            auditEngageActions(action, request, Constants.SUCCESS, null);

        } catch (Exception ex) {
            auditEngageActions(action, request, Constants.FAILED, ex.getMessage());
            throw ex;
        }
    }

    @Override
    public void languageUpdate(LanguageDetectResponse languageDetectResponse) {
        try {
            LOGGER.info("Request received to update language for payload: {}",languageDetectResponse);
            Integer feedId = languageDetectResponse.getRawFeedId();
            EngageNotificationDetails documentFromEs = getFeedDocumentFromEs(feedId);

            if(Objects.isNull(documentFromEs)) {
                LOGGER.info("No doc found in ES to be updated for request {}", languageDetectResponse);
                return;
            }

            documentFromEs.setIsEnglish(languageDetectResponse.getLanguageMatch());

            String doc = JSONUtils.toJSON(documentFromEs, JsonInclude.Include.NON_NULL);
            esService.updateDocument(doc, ElasticConstants.SOCIAL_ENGAGE_V2_FEED.getName(), documentFromEs.getRawFeedId());
            LOGGER.info("details found for request {} enterpriseId : {}", documentFromEs, languageDetectResponse.getEnterpriseId());
            if(Objects.nonNull(languageDetectResponse.getIsHidden()) && !languageDetectResponse.getIsHidden()) {
                insertNewNotificationInFirebase(languageDetectResponse.getEnterpriseId(), languageDetectResponse.getBusinessId());
                //insertEngageFeedEsUpdateInFirebase(languageDetectResponse.getEnterpriseId(), languageDetectResponse.getBusinessId(), documentFromEs);
            }
        } catch (Exception ex) {
            LOGGER.info("Error while updating language for payload: {}",languageDetectResponse);
        }
    }

    @Override
    public void likeMessage(SocialEngageObjectRequest request) {
        LOGGER.info("Request ot like message from inbox : {}",request);
        EngageV2FeedSubTypeEnum engageV2FeedSubTypeEnum =
                Boolean.TRUE.equals(request.getIsLiked()) ? EngageV2FeedSubTypeEnum.LIKE : EngageV2FeedSubTypeEnum.UNLIKE;
        try {
            ExternalServiceEvent externalServiceEvent = new ExternalServiceEvent();
            SocialEngageV2 execute = engageFactory.getSocialEngageChannel(request.getChannel())
                    .orElseThrow(() -> new IllegalArgumentException(INVALID_REQUEST));
            String feedId = request.getFeedId();
            Boolean like = request.getIsLiked();
            EngageNotificationDetails documentFromEs = getFeedDocumentFromEs(request.getRawFeedId());
            if(Objects.isNull(documentFromEs)) {
                LOGGER.info(NO_DOC_FOUND_LOG_MSG, feedId);
                return;
            }
            if(like) {
                execute.likeMessage(request,externalServiceEvent);
            } else {
                execute.unLikeMessage(request,externalServiceEvent);
            }
            documentFromEs.setIsLikedByAdmin(like);
            String doc = JSONUtils.toJSON(documentFromEs, JsonInclude.Include.NON_NULL);
            esService.updateDocument(doc, ElasticConstants.SOCIAL_ENGAGE_V2_FEED.getName(), documentFromEs.getRawFeedId());
            auditEngageActions(engageV2FeedSubTypeEnum.name(), request, Constants.SUCCESS, null);
        }catch (BirdeyeSocialException e){
            LOGGER.info("Error occurred while updating is liked by admin : {}",e.getLocalizedMessage());
            auditEngageActions(engageV2FeedSubTypeEnum.name(), request, Constants.FAILED, e.getMessage());
            throw new BirdeyeSocialException(e.getErrorCode(),e.getMessage());
        }catch(Exception e){
            LOGGER.info("Error occurred while updating is liked by admin : {}",e.getLocalizedMessage());
            auditEngageActions(engageV2FeedSubTypeEnum.name(), request, Constants.FAILED, e.getMessage());
            throw new SocialException(INTERNAL_ERROR_MSG, ErrorCodes.INTERNAL_SERVER_ERROR.value());
        }
    }

    @Override
    public void socialPropertyEventHandler(BusinessPropertyEventRequest request) {
        try {
            Long enterpriseId =  request.getBusinessNumber();
            Integer oldSocialEnabled = request.getOldSocialEnabled();
            Integer newSocialEnabled = request.getIsSocialEnabled();

            Boolean oldSubscriptionStatus = SocialPropertyStatusEnum.engageStatus(oldSocialEnabled);
            Boolean newSubscriptionStatus = SocialPropertyStatusEnum.engageStatus(newSocialEnabled);

            Boolean subscribe;
            if(Boolean.TRUE.equals(oldSubscriptionStatus) && Boolean.FALSE.equals(newSubscriptionStatus)) {
                subscribe = false;
            } else if (Boolean.FALSE.equals(oldSubscriptionStatus) && Boolean.TRUE.equals(newSubscriptionStatus)) {
                subscribe = true;
            } else {
                LOGGER.info("No subscription required for request {}", request);
                return;
            }


            Arrays.asList(SocialChannel.FACEBOOK.getName(), SocialChannel.INSTAGRAM.getName(), SocialChannel.TWITTER.getName(),
                    SocialChannel.LINKEDIN.getName()).stream().forEach(channel -> webhookEventUpdate(enterpriseId, subscribe, channel));


        } catch (Exception ex) {
            LOGGER.info("Error occured while fetching page details ", ex);
        }
    }

    @Override
    public void mappingUpdate(SocialSetupAudit request) {
        try {
            SocialEngageV2 execute = engageFactory.getSocialEngageChannel(request.getChannel())
                    .orElseThrow(() -> new IllegalArgumentException(INVALID_REQUEST));

           String action = request.getAction();

            EngageWebhookSubscriptionRequest req = unsubscribeRequest(request);

            if(SocialSetupAuditEnum.REMOVE_PAGE.name().equalsIgnoreCase(action)) {
                execute.removePageNotificationWebhook(req);
            } else if(SocialSetupAuditEnum.REMOVE_MAPPING.name().equalsIgnoreCase(action)) {
                String pageId = req.getPageId();
                execute.unSubscribeNotificationWebhook(pageId);
            }
        } catch (Exception ex) {
            LOGGER.info("Error occured while fetching page details ", ex);
        }
    }

    public void updateTags(SocialTagEntityMappingActionEvent tagEntityMappingActionEvent) {
        Long rawFeedId = tagEntityMappingActionEvent.getEntityId();
        SocialTagEntityType entityType = tagEntityMappingActionEvent.getEntityType();

        validateUpdateTagRequest(rawFeedId, entityType);

        List<SocialTagBasicDetail> basicDetailList = socialTagService.getBasicTagDetailForSingleEntityId(rawFeedId, SocialTagEntityType.ENGAGE);

        try {
            Map<String, Object> params = new HashMap<>();
            if(CollectionUtils.isNotEmpty(basicDetailList)) {
                params.put("tagIds", basicDetailList.stream().map(SocialTagBasicDetail::getId).collect(Collectors.toSet()));
            } else {
                params.put("tagIds", null);
            }
            esService.updateDocument(JSONUtils.toJSON(params), ElasticConstants.SOCIAL_ENGAGE_V2_FEED.getName(), Math.toIntExact(rawFeedId));
        } catch (Exception ex) {
            LOGGER.info("Something went wrong while updating tags for raw feed id {}", rawFeedId, ex);
        }

    }

    private void validateUpdateTagRequest(Long rawFeedId, SocialTagEntityType entityType) {
        LOGGER.info("Validating update tag request for raw feed id {} and entity type {}", rawFeedId, entityType);
        if (Objects.isNull(rawFeedId)) {
            throw new BadRequestException("Entity id should not be null");
        }

        if (!Objects.equals(SocialTagEntityType.ENGAGE, entityType)) {
            throw new BadRequestException("Entity type should be ENGAGE only");
        }
    }

    @Override
    public void reIndexEngageIndex(String fromIndexName, Long count, Integer from, Integer pageSize) {
        try {
            if(Objects.isNull(count))  count = getESDataCount(fromIndexName);
            if(Objects.isNull(pageSize)) pageSize = 10000;
            if(Objects.isNull(from)) from = 0;
            while(from <= count){
                insertDataInReindexESTopic(fromIndexName, from, pageSize);
                from = from + pageSize;
            }

        }catch(Exception ex) {
            LOGGER.info("Error occured while reIndexEngageIndex ", ex);
        }
    }

    private void insertDataInReindexESTopic(String fromIndexName, Integer from, Integer pageSize) {
        kafkaProducerService.sendObjectV1(KafkaTopicEnum.ENGAGE_REINDEX_EVENT.getName(), new EngageReIndexEsRequest(fromIndexName, pageSize , from));
    }

    @Override
    public void insertDataInReindexESDoc(EngageReIndexEsRequest request) {
        try {
            SearchRequest searchRequest = new SearchRequest(request.getFromIndexName());
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
            searchSourceBuilder.query(QueryBuilders.matchAllQuery());
            searchRequest.source(searchSourceBuilder);
            searchSourceBuilder.size(request.getSize());
            searchSourceBuilder.from(request.getFrom());
            searchSourceBuilder.sort("feedDate", SortOrder.ASC );
            searchRequest.source(searchSourceBuilder);
            SearchResponse response = esService.search(searchRequest);
            SearchHit[] searchHits = response.getHits().getHits();
            LOGGER.info("reIndexEngageIndex old : {} searchHits :{} from : {}", request.getFromIndexName(), searchHits.length, request.getFrom());
            if (searchHits.length > 0) {
                for (SearchHit hit : searchHits) {
                    try {
                        EngageNotificationDetails old = JSONUtils.fromJSON(hit.getSourceAsString(), EngageNotificationDetails.class);
                        uploadEngageContentToES(old);
                    }catch(Exception e){
                        LOGGER.info("Inner Error occured while reIndexEngageIndex for record : {} and exception", hit.getSourceAsString(), e);
                    }
                }
            }
            LOGGER.info("reIndexEngageIndex Process Completed searchHits :{}", searchHits.length);
        }catch(Exception ex) {
                LOGGER.info("Error occured while reIndexEngageIndex for request : {} and exception", request, ex);
        }
    }

    private long getESDataCount(String fromIndexName) {
        long count = 0;
        try {
            CountRequest countRequest = new CountRequest(fromIndexName);
            CountResponse response = esService.countDocument(countRequest);
            count = response.getCount();
        }catch(Exception ex) {
            LOGGER.info("Error occured while reIndexEngageIndex ", ex);
        }
        return count;
    }

    private EngageWebhookSubscriptionRequest unsubscribeRequest(SocialSetupAudit setupAudit) {
        EngageWebhookSubscriptionRequest req = new EngageWebhookSubscriptionRequest();
        if(setupAudit.getChannel().equalsIgnoreCase(SocialChannel.FACEBOOK.getName())) {
            BusinessFBPage fbPage = JSONUtils.fromJSON(setupAudit.getEntity(), BusinessFBPage.class);
            req.setPageId(fbPage.getFacebookPageId());
            req.setPageAccessToken(fbPage.getPageAccessToken());
            req.setChannel(SocialChannel.FACEBOOK.getName());
        } else if(setupAudit.getChannel().equalsIgnoreCase(SocialChannel.INSTAGRAM.getName())) {
            BusinessInstagramAccount instagramAccount = JSONUtils.fromJSON(setupAudit.getEntity(), BusinessInstagramAccount.class);
            req.setPageId(instagramAccount.getFacebookPageId());
            req.setPageAccessToken(instagramAccount.getPageAccessToken());
            req.setChannel(SocialChannel.INSTAGRAM.getName());
        }  else if(setupAudit.getChannel().equalsIgnoreCase(SocialChannel.LINKEDIN.getName())) {
            BusinessLinkedinPage linkedinPage = JSONUtils.fromJSON(setupAudit.getEntity(), BusinessLinkedinPage.class);
            req.setPageId(linkedinPage.getUrn());
            req.setPageAccessToken(linkedinPage.getAccessToken());
            req.setPersonId(linkedinPage.getPersonUrn());
            req.setChannel(SocialChannel.LINKEDIN.getName());
        } else if(setupAudit.getChannel().equalsIgnoreCase(SocialChannel.TWITTER.getName())) {
            BusinessTwitterAccounts twitterAccount = JSONUtils.fromJSON(setupAudit.getEntity(), BusinessTwitterAccounts.class);
            req.setPageId(String.valueOf(twitterAccount.getProfileId()));
            req.setPageAccessToken(twitterAccount.getAccessToken());
            req.setPageAccessSecret(twitterAccount.getAccessSecret());
            req.setChannel(SocialChannel.TWITTER.getName());
        }

        return req;

    }

    private void webhookEventUpdate(Long enterpriseId, Boolean subscribe, String channel) {
        SocialEngageV2 execute = engageFactory.getSocialEngageChannel(channel)
                .orElseThrow(() -> new IllegalArgumentException(INVALID_REQUEST));

        List<String> pageIds = execute.getChannelPageIdsByEnterpriseId(enterpriseId);

        if(CollectionUtils.isEmpty(pageIds)) {
            LOGGER.info("No valid pages found for channel {}, existing", channel);
            return;
        }

        pageIds.stream().forEach(pageId -> {

            if(subscribe) {
                execute.subscribeNotificationWebhook(pageId);
            } else {
                execute.unSubscribeNotificationWebhook(pageId);
            }

        });
    }

    private Boolean isValidUrl(String imageUrl)
    {
        List<String> request=Arrays.asList(imageUrl);
        List<Boolean> response=picturesqueService.validateImageUrls(request);
        if(Objects.nonNull(response) && CollectionUtils.isNotEmpty(response))
        {
            return response.get(0);
        }
        return false;
    }

    @Override
    public void saveMedia(Map<String, Object> uploadRequestMap) {
        if(MapUtils.isEmpty(uploadRequestMap)) {
            LOGGER.info("empty callback request from picturesque");
        }

        PicturesqueMediaCallback callback = new PicturesqueMediaCallback();
        if(Objects.isNull(uploadRequestMap.get("id"))) {
            callback.setCallBackId((Integer) uploadRequestMap.get("id"));
        }
        if(Objects.nonNull(uploadRequestMap.get("midSizeVersionUrl"))) {
            Map data = (Map) uploadRequestMap.get("midSizeVersionUrl");
            Map value = (Map) data.get("value");
            callback.setMediaUrl(String.valueOf(value.get("value")));
            callback.setHeight((Integer) value.get("height"));
            callback.setWidth((Integer) value.get("width"));
        }

        if(Objects.nonNull(uploadRequestMap.get("location"))) {
            String medialUrl = (String) uploadRequestMap.get("location");
            callback.setMediaUrl(medialUrl);

            callback.setHeight((Integer) uploadRequestMap.get("height"));
            callback.setWidth((Integer) uploadRequestMap.get("width"));
        }

        if(Objects.nonNull(callback.getHeight()) && Objects.nonNull(callback.getWidth())
                && Objects.isNull(callback.getAspectRatio()))  {
            callback.setAspectRatio((double) callback.getWidth() / (double) callback.getHeight());
        }

        sendCallBackEventWithKey(callback);
    }

    private void sendCallBackEventWithKey(PicturesqueMediaCallback callback) {
        if(Objects.isNull(callback) || Objects.isNull(callback.getCallBackId())) {
            LOGGER.info("empty callback request from picturesque");
            return;
        }
        MediaAsset mediaAsset = mediaAssetRepoService.findById(callback.getCallBackId());

        if(Objects.isNull(mediaAsset)) {
            LOGGER.info("no media data found for call back id: {}", callback.getCallBackId());
            return;
        }

        kafkaProducerService.sendWithKey(KafkaTopicEnum.MEDIA_UPLOAD_CALLBACK.getName(), mediaAsset.getEntityId(), callback);
        //kafkaProducerService.sendObjectV1(KafkaTopicEnum.MEDIA_UPLOAD_CALLBACK.getName(), callback);
    }

    @Override

    public void saveMediaForEngageInEs(PicturesqueMediaCallback callback) {
        if(Objects.isNull(callback) || Objects.isNull(callback.getCallBackId())) {
            LOGGER.info("empty callback request from picturesque");
            return;
        }
        MediaAsset mediaAsset = mediaAssetRepoService.findById(callback.getCallBackId());

        if(Objects.isNull(mediaAsset)) {
            LOGGER.info("no media data found for call back id: {}", callback.getCallBackId());
            return;
        }

        try {
            EngageNotificationDetails esDoc =  getFeedDocumentFromEs(Integer.parseInt(mediaAsset.getEntityId()));
            updateUrlsInEsDoc(callback, esDoc, mediaAsset);
            upsertEngageEsDocument(esDoc);
            EngageFeedDetails engageFeedDetails = engageFeedDetailsRepo.findById(esDoc.getRawFeedId());
            if(Objects.nonNull(engageFeedDetails)) {
                engageFeedDetails.setMetaData(JSONUtils.toJSON(esDoc));
                engageFeedDetailsRepo.save(engageFeedDetails);
            }
            mediaAssetRepoService.deleteById(callback.getCallBackId());
        } catch (Exception e) {
            LOGGER.info("exception occurred while updating media url in es for request: {}, error: {}", callback, e.getMessage());
        }
    }

    private void updateUrlsInEsDoc(PicturesqueMediaCallback callback, EngageNotificationDetails esDoc, MediaAsset mediaAsset) throws Exception {
        List<MessageAttachment> attachments = new ArrayList<>();
        SocialEngageV2 execute = engageFactory.getSocialEngageChannel(esDoc.getChannel())
                .orElseThrow(() -> new IllegalArgumentException(INVALID_REQUEST));
        EngageBusinessDetails pageBusinessDetails = execute.getChannelEnterpriseIdByPageId(esDoc.getPageId());
        if(Objects.isNull(pageBusinessDetails)) {
            LOGGER.info("null pageBusinessDetails for callback request: {}", callback);
            throw new Exception("pageBusinessDetails");
        }

        String enterpriseId = String.valueOf(pageBusinessDetails.getEnterpriseId());
        String originalMediaUrl = mediaAsset.getMediaUrl();

        MediaData m = getMediaDataForPicturesqueCallback(callback, enterpriseId);
        if(StringUtils.isNotEmpty(esDoc.getAuthorProfileImage()) && isValidUrl(esDoc.getAuthorProfileImage()) &&
                esDoc.getAuthorProfileImage().equals(originalMediaUrl)) {
            esDoc.setAuthorProfileImage(m.getMediaUrl());
        }

        if(CollectionUtils.isNotEmpty(esDoc.getImageUrls())) {
            List<MediaData> imageMetadata = new ArrayList<>();
            List<String> imageCdnUrl = new ArrayList<>();
            for(Object url:  esDoc.getImageUrls()) {
                String u = (String) url;
                if(StringUtils.isNotEmpty(u) && u.equals(originalMediaUrl)) {
                    if (esDoc.getType().equalsIgnoreCase(EngageV2FeedTypeEnum.MESSAGE.name())) {
                        attachments = createMediaEventForInbox(m, u, attachments);
                    }
                    imageMetadata.add(m);
                    if (Objects.nonNull(m)) {
                        imageCdnUrl.add(m.getMediaUrl());
                    }
                } else {
                    imageCdnUrl.add(u);
                    imageMetadata.add(new MediaData(u, null));
                }
            }
            esDoc.setImageUrls(imageCdnUrl);
            esDoc.setImageUrlsMetaData(imageMetadata);

        }

        if(CollectionUtils.isNotEmpty(esDoc.getVideoUrls())) {
            List<MediaData> videoMetadata = new ArrayList<>();
            List<String> videoCdnUrl = new ArrayList<>();

            for(Object url:  esDoc.getVideoUrls()) {
                String u = (String) url;
                if(StringUtils.isNotEmpty(u) && u.equals(originalMediaUrl)) {
                    if (esDoc.getType().equalsIgnoreCase(EngageV2FeedTypeEnum.MESSAGE.name())) {
                        attachments = createMediaEventForInbox(m, u, attachments);
                    }
                    videoMetadata.add(m);
                    if (Objects.nonNull(m)) {
                        videoCdnUrl.add(m.getMediaUrl());
                    }
                } else {
                    videoCdnUrl.add(u);
                    videoMetadata.add(new MediaData(u, null));
                }
            }
            esDoc.setVideoUrls(videoCdnUrl);
            esDoc.setVideoUrlsMetaData(videoMetadata);
        }
        if(esDoc.getType().equalsIgnoreCase(EngageV2FeedTypeEnum.MESSAGE.name()) &&
                CollectionUtils.isNotEmpty(attachments)){
            sendEventToInbox(esDoc, attachments, pageBusinessDetails);
        }
        if(CollectionUtils.isNotEmpty(esDoc.getThumbnails())) {
            List<MediaData> imageMetadata = new ArrayList<>();
            List<String> imageCdnUrl = new ArrayList<>();
            for(Object url:  esDoc.getThumbnails()) {
                String u = (String) url;
                if(StringUtils.isNotEmpty(u) && u.equals(originalMediaUrl)) {
                    imageMetadata.add(m);
                    if (Objects.nonNull(m)) {
                        imageCdnUrl.add(m.getMediaUrl());
                    }
                } else {
                    imageCdnUrl.add(u);
                    imageMetadata.add(new MediaData(u, null));
                }
            }
            esDoc.setThumbnails(imageCdnUrl);
            esDoc.setThumbnailsMetaData(imageMetadata);

        }
        if(Objects.nonNull(esDoc.getSubEvent())) {
            if(StringUtils.isNotEmpty(esDoc.getSubEvent().getAuthorProfileImage())) {
                if(esDoc.getSubEvent().getAuthorProfileImage().equals(originalMediaUrl)) {
                    esDoc.getSubEvent().setAuthorProfileImage(m.getMediaUrl());
                }
            }
            if (CollectionUtils.isNotEmpty(esDoc.getSubEvent().getImageUrls())) {
                List<MediaData> imageMetadata = new ArrayList<>();
                List<String> imageCdnUrl = new ArrayList<>();

                for (Object url : esDoc.getSubEvent().getImageUrls()) {
                    String u = (String) url;
                    if(StringUtils.isNotEmpty(u) && u.equals(originalMediaUrl)) {
                        imageMetadata.add(m);
                        if (Objects.nonNull(m)) {
                            imageCdnUrl.add(m.getMediaUrl());
                        }
                    } else {
                        imageCdnUrl.add(u);
                        imageMetadata.add(new MediaData(u, null));
                    }
                }
                esDoc.getSubEvent().setImageUrls(imageCdnUrl);
                esDoc.getSubEvent().setImageUrlsMetaData(imageMetadata);
            }
            if (CollectionUtils.isNotEmpty(esDoc.getSubEvent().getVideoUrls())) {
                List<MediaData> videoMetadata = new ArrayList<>();
                List<String> videoCdnUrl = new ArrayList<>();

                for (Object url : esDoc.getSubEvent().getVideoUrls()) {
                    String u = (String) url;
                    if(StringUtils.isNotEmpty(u) && u.equals(originalMediaUrl)) {
                        videoMetadata.add(m);
                        if (Objects.nonNull(m)) {
                            videoCdnUrl.add(m.getMediaUrl());
                        }
                    } else {
                        videoCdnUrl.add(u);
                        videoMetadata.add(new MediaData(u, null));
                    }
                }
                esDoc.getSubEvent().setVideoUrls(videoCdnUrl);
                esDoc.getSubEvent().setVideoUrlsMetaData(videoMetadata);
            }
        }
    }

    @Override
    public void updateIsDeletedForComments(List<Long> businessNumber) {
        try {
            List<String> pages = new ArrayList<>();
            List<BusinessFBPage> fbPages=  socialFBPageRepository.findFacebookPageIdByEnterpriseIdIn(businessNumber);
            List<BusinessInstagramAccount> igAccounts=  instagramAccountRepository.findDistinctInstagramIdByEnterpriseIn(businessNumber);
            List<BusinessLinkedinPage> linkedinPages=  linkedinRepo.findDistinctPageIdByEnterpriseIdIn(businessNumber);
           // List<BusinessTwitterAccounts> twitterAccounts=  socialTwitterAccountRepository.findProfileIdsByEnterpriseIdIn(businessNumber);
          //  List<BusinessYoutubeChannel> ytPages=  youtubeChannelRepository.findDistinctChannelIdByEnterpriseIdIn(businessNumber);
            if (CollectionUtils.isNotEmpty(fbPages)) {
                pages.addAll(fbPages.stream().map(BusinessFBPage :: getFacebookPageId).collect(Collectors.toList()));
            }
            if (CollectionUtils.isNotEmpty(igAccounts)) {
                pages.addAll(igAccounts.stream().map(BusinessInstagramAccount::getInstagramAccountId).collect(Collectors.toList()));
            }
            if (CollectionUtils.isNotEmpty(linkedinPages)) {
                pages.addAll(linkedinPages.stream().map(BusinessLinkedinPage::getProfileId).collect(Collectors.toList()));
            }

            List<EngageNotificationDetails> fbFeedDetailsList = new ArrayList<>();
            List<EngageNotificationDetails> igFeedDetailsList = new ArrayList<>();
            List<EngageNotificationDetails> linkedinFeedDetailsList = new ArrayList<>();

            SearchRequest searchRequest = new SearchRequest( ElasticConstants.SOCIAL_ENGAGE_V2_FEED.getName());
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();

            BoolQueryBuilder b = new BoolQueryBuilder();
            b.must(QueryBuilders.termsQuery("pageId.keyword", pages));
            b.must(QueryBuilders.termQuery(IS_DELETED_ES, false));
            b.must(QueryBuilders.termsQuery("type.keyword", Arrays.asList(COMMENT,AD_COMMENT)));
            searchSourceBuilder.query(b);
            searchSourceBuilder.size(5000);
            searchRequest.source(searchSourceBuilder);
            SearchResponse response = esService.search(searchRequest);
            SearchHit[] searchHits = response.getHits().getHits();
            if(searchHits.length > 0) {
                for (SearchHit hit : searchHits) {
                    EngageNotificationDetails details = JSONUtils.fromJSON(hit.getSourceAsString(), EngageNotificationDetails.class);
                    String channel = details.getChannel();
                    switch (channel.toLowerCase()) {
                        case "facebook":
                            fbFeedDetailsList.add(details);
                            break;
                        case "instagram":
                            igFeedDetailsList.add(details);
                            break;
                        case "linkedin":
                            linkedinFeedDetailsList.add(details);
                            break;
                        default:
                            // Optionally handle unknown channels or log them
                            break;
                    }
                }
                if(CollectionUtils.isNotEmpty(fbFeedDetailsList)) {
                    updateFBESComment(fbFeedDetailsList, fbPages);
                }else
                {
                    LOGGER.info("No data found in ES for businessNumber :{} for Facebook",businessNumber);
                }
                if(CollectionUtils.isNotEmpty(igFeedDetailsList)) {
                    updateIGESComment(igFeedDetailsList,igAccounts);
                }else
                {
                    LOGGER.info("No data found in ES for businessNumber :{} for IG",businessNumber);
                }
                if(CollectionUtils.isNotEmpty(linkedinFeedDetailsList)) {
                    updateLinkedinESComment(linkedinFeedDetailsList,linkedinPages);
                }else
                {
                    LOGGER.info("No data found in ES for businessNumber :{} for Linkedin",businessNumber);
                }
            }


        } catch (Exception ex) {
            LOGGER.info("Exception occurred while updating isDeleted flag in ES for businessNumber :{} with exception :{}",businessNumber,ex);
        }

    }



    public String extractCommentIdFromUrn(String commentUrn) {
        return commentUrn.contains("urn:li:activity") || commentUrn.contains("urn:li:comment")?
                commentUrn.substring(commentUrn.indexOf(",")+1, commentUrn.length()-1)
                : commentUrn.substring(commentUrn.lastIndexOf(",")+1);

    }

    private void updateLinkedinESComment(List<EngageNotificationDetails> linkedinFeedDetailsList, List<BusinessLinkedinPage> linkedinPages) {

        Map<String, List<String>> accessTokenToFeedIdsMap = linkedinPages.stream()
                .collect(Collectors.toMap(
                        BusinessLinkedinPage::getAccessToken, // Key mapper
                        page -> findFeedIdsForPage(linkedinFeedDetailsList, page.getProfileId())
                ));
        List<String> feedIdIsDeleted= new ArrayList<>();
        accessTokenToFeedIdsMap.forEach((accessToken, feedIdsList) -> {
            LOGGER.info("Access Token:{} and Feed IDs:{} " , accessToken , feedIdsList);
            // Call getCommentExistById for each feedId in feedIdsList
            for (String feedId : feedIdsList) {
                String commentId=extractCommentIdFromUrn(feedId);
                Boolean commentExists= linkedinService.getCommentsDeleteDetails(accessToken,feedId,commentId);
                LOGGER.info("Feed ID:{}  Comment Exists:{} ", feedId, commentExists);
                if(commentExists)
                    feedIdIsDeleted.add(feedId);
            }
        });
        if(CollectionUtils.isNotEmpty(feedIdIsDeleted)) {
            LOGGER.info("Feed Id received for marking is Deleted for Linkedin:{}",feedIdIsDeleted);
            updateESDeleteFeedId(feedIdIsDeleted);
        }
        else
        {
            LOGGER.info("No data to update for Linkedin");
        }
    }

    private void updateIGESComment(List<EngageNotificationDetails> igFeedDetailsList, List<BusinessInstagramAccount> igAccounts) {
        Map<String, List<String>> accessTokenToFeedIdsMap = igAccounts.stream()
                .collect(Collectors.toMap(
                        BusinessInstagramAccount::getPageAccessToken, // Key mapper
                        page -> findFeedIdsForPage(igFeedDetailsList, page.getInstagramAccountId())
                ));
        List<String> feedIdIsDeleted= new ArrayList<>();
        accessTokenToFeedIdsMap.forEach((accessToken, feedIdsList) -> {
            LOGGER.info("Access Token:{} and Feed IDs:{} " , accessToken , feedIdsList);
            // Call getCommentExistById for each feedId in feedIdsList
            for (String feedId : feedIdsList) {
                boolean commentExists = instagramService.getCommentDeleteDetails(accessToken,feedId);
                LOGGER.info("Feed ID:{}  Comment Exists:{} ", feedId, commentExists);
                if(commentExists)
                    feedIdIsDeleted.add(feedId);
            }
        });

        if(CollectionUtils.isNotEmpty(feedIdIsDeleted)) {
            LOGGER.info("Feed Id received for marking is Deleted for Instagram :{}",feedIdIsDeleted);
            updateESDeleteFeedId(feedIdIsDeleted);
        }
        else
        {
            LOGGER.info("No data to update for Instagram");
        }
    }

    private void updateFBESComment(List<EngageNotificationDetails> fbFeedDetailsList, List<BusinessFBPage> fbPages) {
        Map<String, List<String>> accessTokenToFeedIdsMap = fbPages.stream()
                .collect(Collectors.toMap(
                        BusinessFBPage::getPageAccessToken, // Key mapper
                        page -> findFeedIdsForPage(fbFeedDetailsList, page.getFacebookPageId())
                ));
        List<String> feedIdIsDeleted= new ArrayList<>();
        accessTokenToFeedIdsMap.forEach((accessToken, feedIdsList) -> {
            LOGGER.info("Access Token:{} and Feed IDs:{} " , accessToken , feedIdsList);
            // Call getCommentExistById for each feedId in feedIdsList
            for (String feedId : feedIdsList) {
                boolean commentExists = facebookService.getCommentExistById(feedId,accessToken);
                LOGGER.info("Feed ID:{}  Comment Exists:{} ", feedId, commentExists);
                if(commentExists)
                    feedIdIsDeleted.add(feedId);
            }
        });
        if(CollectionUtils.isNotEmpty(feedIdIsDeleted)) {
            LOGGER.info("Feed Id received for marking is Deleted for Facebook:{}",feedIdIsDeleted);
            updateESDeleteFeedId(feedIdIsDeleted);
        }
        else
        {
            LOGGER.info("No data to update for Linkedin");
        }

    }

    private void updateESDeleteFeedId(List<String> feedIdIsDeleted) {
        try {
            UpdateByQueryRequest updateRequest = new UpdateByQueryRequest(ElasticConstants.SOCIAL_ENGAGE_V2_FEED.getName());
            updateRequest.setQuery(QueryBuilders.termsQuery("feedId.keyword", feedIdIsDeleted));

            Map<String, Object> data = new HashMap<>();
            data.put(VALUE, true);
            Script inline = new Script(ScriptType.INLINE, PAINLESS, SOURCE_DELETE, data);
            updateRequest.setScript(inline);
            LOGGER.info(QUERY_DOC_LOG_MSG , updateRequest);

            BulkByScrollResponse response = esService.updateByQueryRequest(updateRequest);

            long updatedDocuments = response.getUpdated();
            LOGGER.info(UPDATED_DOC_LOG_MSG , updatedDocuments);


        } catch (Exception ex) {
            LOGGER.info("Error while updating original comment {}", feedIdIsDeleted);
        }
    }

    private  List<String> findFeedIdsForPage(List<EngageNotificationDetails> feedDetailsList, String pageId) {
        return feedDetailsList.stream()
                .filter(feed -> feed.getPageId().equals(pageId))
                .map(EngageNotificationDetails::getFeedId)
                .collect(Collectors.toList());
    }

    @Override
    public void backFillTrendsData(TrendsBackfillRequest slaBackfillRequest) {
        String indexName = ElasticConstants.SOCIAL_ENGAGE_V2_FEED.getName();

        try {
            Date startDate = Objects.nonNull(slaBackfillRequest.getStartDate())
            ? slaBackfillRequest.getStartDate() : DateUtils.addDays(new Date(), -30) ;
            Date endDate = Objects.nonNull(slaBackfillRequest.getStartDate())
            ? slaBackfillRequest.getEndDate()  : new Date() ;
            DateTimeUtils.setDatesToExtremeOfTime(startDate, endDate);
            PageDetailsData mapLocationPageId = new PageDetailsData();
            if (!CollectionUtils.isEmpty(slaBackfillRequest.getSocialChannels())) {

                for (String ch : slaBackfillRequest.getSocialChannels()) {
                    SocialEngageV2 execute = engageFactory.getSocialEngageChannel(ch)
                            .orElseThrow(() -> new IllegalArgumentException(INVALID_REQUEST));
                     PageDetailsData pageDetails = execute.getChannelPageIdsByEnterpriseIds(slaBackfillRequest.getEnterpriseIds());
                    mapLocationPageId.getPageDetailsList().addAll(pageDetails.getPageDetailsList());
                }
            }

            List<String> pageIds= mapLocationPageId.getPageDetailsList().stream()
                    .map(PageDetailsData.PageDetails::getPageId)
                    .collect(Collectors.toList());
            //GET LAST 30 days post data
            SearchRequest postSearchRequest = new SearchRequest(indexName);

            // Build the search query
            SearchSourceBuilder sourceBuilder1 = new SearchSourceBuilder();
            List<String> postFeedTypes = EngageV2PublicFeedTypeEnum.getBackFillPostFeedTypes(slaBackfillRequest.getMessages());
            sourceBuilder1.query(QueryBuilders.boolQuery()
                    .must(QueryBuilders.termsQuery("type.keyword", postFeedTypes))
                    .must(QueryBuilders.rangeQuery(FEED_DATE)
                    .gte(DateTimeUtils.convertDateToESDateTime(startDate))
                    .lte(DateTimeUtils.convertDateToESDateTime(endDate)))
                    .must(QueryBuilders.termsQuery(PAGE_ID, pageIds)))
                    .fetchSource(new String[] {"feedId","type"}, null);

            postSearchRequest.source(sourceBuilder1);

            SearchResponse postResponse=esService.search(postSearchRequest);

            //Main Post feedId
            List<String> postFeedIds=new ArrayList<>();
            List<String> dmFeedIds=new ArrayList<>();
            for (SearchHit hit : postResponse.getHits().getHits()) {
                if("MESSAGE".equalsIgnoreCase((String)hit.getSourceAsMap().get("type"))){
                    dmFeedIds.add((String)hit.getSourceAsMap().get("feedId"));
                }
                else
                    postFeedIds.add((String)hit.getSourceAsMap().get("feedId"));
            }

            Map<String, Map<String, Object>> esDocToUpdate = new HashMap<>();

            if (!CollectionUtils.isEmpty(postFeedIds)) {
                Map<String, Map<String, Object>> postFeedResult = processFeedIds(mapLocationPageId, postFeedIds, pageIds, startDate, endDate, indexName, false);
                if (postFeedResult != null) {
                    // Merge the results into esDocToUpdate
                    esDocToUpdate.putAll(postFeedResult);  // Merges the map from postFeedIds into the main map
                }
            }

            if (!CollectionUtils.isEmpty(dmFeedIds)) {
                Map<String, Map<String, Object>> dmFeedResult = processFeedIds(mapLocationPageId, dmFeedIds, pageIds, startDate, endDate, indexName, true);
                if (dmFeedResult != null) {
                    // Merge the results into esDocToUpdate
                    esDocToUpdate.putAll(dmFeedResult);  // Merges the map from dmFeedIds into the main map
                }
            }
            updateDocumentInBulk(esDocToUpdate);
            }catch (Exception e) {
           LOGGER.error("Exception occurred while backill trends data :{}",e.getMessage());
        }
    }

    private void updateDocumentInBulk(Map<String, Map<String, Object>> esDocToUpdate) {
        // Create a list to hold all UpdateRequests
        List<UpdateRequest> updateRequests = new ArrayList<>();

        // Iterate through each entry in the esDocToUpdate map
        for (Map.Entry<String, Map<String, Object>> entry : esDocToUpdate.entrySet()) {
            String rawFeedID = entry.getKey();  // This is the document ID
            Map<String, Object> parameterMap = entry.getValue();  // The document content to update

            try {
                UpdateRequest updateRequest = new UpdateRequest();
                updateRequest.id(rawFeedID)
                        .docAsUpsert(false)
                        .doc(JSONUtils.toJSON(parameterMap), XContentType.JSON)
                        .index(ElasticConstants.SOCIAL_ENGAGE_V2_FEED.getName());

                // Add the update request to the list
                updateRequests.add(updateRequest);
            } catch (Exception e) {
                LOGGER.error("Error creating update request for rawFeedID {}: {}", rawFeedID, e.getMessage());
            }
        }

        // If there are update requests, execute the bulk update
        if (!updateRequests.isEmpty()) {
            try {
                esService.updateDocumentInBulk(updateRequests);
            } catch (Exception e) {
                LOGGER.error("Exception occurred while updating documents in bulk with error: {}", e.getMessage());
            }
        } else {
            LOGGER.warn("No update requests were created as the map was empty.");
        }
    }


    private Map<String, Map<String, Object>> processFeedIds(PageDetailsData mapLocationPageId,List<String> feedIds, List<String> pageIds, Date startDate, Date endDate, String indexName, boolean isDM) throws IOException {

        if (!CollectionUtils.isEmpty(feedIds)) {
            List<EngageNotificationDetails> parentDocList = getParentDocData(feedIds, startDate, endDate, indexName, isDM);

            List<String> repliedFeedIds = parentDocList.stream()
                    .map(EngageNotificationDetails::getFeedId)
                    .collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(repliedFeedIds)) {
                List<EngageNotificationDetails> childDocList;

                if (isDM) {
                    childDocList = getChildDMDocData(pageIds, repliedFeedIds, indexName);
                } else {
                    childDocList = getChildCMDocData(pageIds, repliedFeedIds, indexName);
                }

                // Update Elasticsearch documents
               return updateESDoc(childDocList, parentDocList, mapLocationPageId, indexName);
            }
        }
        return null;
    }


    private Map<String,Map<String, Object>> updateESDoc(List<EngageNotificationDetails> childDocumentsList, List<EngageNotificationDetails> parentDocumentsList,
                             PageDetailsData mapLocationPageId,String indexName) {

        Map<String, Map<String, Object>> updateDoc = new HashMap<>();
        for (EngageNotificationDetails details : childDocumentsList) {
            EngageNotificationDetails parentDetails =
                    parentDocumentsList.stream()
                            .filter(feed -> feed.getFeedId().equals(details.getEventParentId()))
                            .findFirst()
                            .orElse(null);
            Integer businessId = Optional.ofNullable(mapLocationPageId.getPageDetailsList())
                    .orElse(Collections.emptyList())
                    .stream()
                    .filter(d -> d != null && d.getPageId() != null && d.getPageId().equals(parentDetails.getPageId()))
                    .map(d -> d.getBusinessId())
                    .filter(Objects::nonNull)
                    .findFirst()
                    .orElse(null);

            Integer accountId = Optional.ofNullable(mapLocationPageId.getPageDetailsList())
                    .orElse(Collections.emptyList())
                    .stream()
                    .filter(d -> d != null && d.getPageId() != null && d.getPageId().equals(parentDetails.getPageId()))
                    .map(d -> d.getAccountId())
                    .filter(Objects::nonNull)
                    .findFirst()
                    .orElse(null);

            if (parentDetails != null) {
                engageConverterService.updateResponseTime(parentDetails, details.getFeedDate(), businessId);
                Map<String, Object> params1 = new HashMap<>();
                params1.put("brandReplyId", details.getFeedId());
                params1.put("absoluteResponseTime", parentDetails.getAbsoluteResponseTime());
                params1.put("relativeResponseTime", parentDetails.getRelativeResponseTime());
                params1.put("locationId", businessId);
                params1.put("accountId", accountId);
                updateDoc.put(String.valueOf(parentDetails.getRawFeedId()), params1);
                Map<String, Object> params2 = new HashMap<>();
                params2.put("isBrandReply", true);
                params2.put("repliedOnId", parentDetails.getFeedId());
                params2.put("locationId", businessId);
                params2.put("accountId", accountId);
                updateDoc.put(String.valueOf(parentDetails.getRawFeedId()), params1);

            }
        }
        return updateDoc;
    }

    private List<EngageNotificationDetails> getChildDMDocData(List<String> pageIds, List<String> repliedDMFeedIds, String indexName) throws IOException {
        List<EngageNotificationDetails> childDocumentsList=new ArrayList<>();
        SearchRequest childSearchRequest = new SearchRequest(indexName);

        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();

        // Disable _source to avoid fetching document fields
        searchSourceBuilder.fetchSource(false);

        // Build the query part (bool query with script scoring)
        searchSourceBuilder.query(
                        QueryBuilders.boolQuery()
                                .must(QueryBuilders.termsQuery(EVENT_PARENT_ID_ES.concat(KEYWORD), repliedDMFeedIds))
                                .must(QueryBuilders.termsQuery(PAGE_ID, pageIds))
                                .must(QueryBuilders.termQuery("type.keyword","MESSAGE_SEND"))
        );

        // Sort by feedDate in ascending order
        searchSourceBuilder.sort(FEED_DATE, SortOrder.ASC);

        // Set the size to 0 to only return aggregations
        searchSourceBuilder.size(0);

        // Aggregations: Group by eventParentId and return top document per group by feedDate
        TermsAggregationBuilder aggregation = AggregationBuilders.terms("grouped_by_eventParentId")
                .field(EVENT_PARENT_ID_ES.concat(KEYWORD))
                .size(10000)
                .subAggregation(
                        AggregationBuilders.topHits("top_docs_by_feedDate")
                                .sort(FEED_DATE, SortOrder.ASC) // Sort by feedDate in ascending order
                                .size(1) // Limit to 1 document per group
                );
        searchSourceBuilder.aggregation(aggregation);

        childSearchRequest.source(searchSourceBuilder);

        // Execute the search query
        SearchResponse childSearchResponse = esService.search(childSearchRequest);


        //Get Child Document from aggregation
        if (childSearchResponse == null || childSearchResponse.getAggregations() == null) {
            LOGGER.error("SearchResponse or Aggregations is null. Returning empty data.");
        }


        ParsedTerms terms = childSearchResponse.getAggregations().get("grouped_by_eventParentId");
        List<? extends Terms.Bucket> buckets = terms.getBuckets();

        if (CollectionUtils.isEmpty(buckets)) {
            LOGGER.error("buckets is null. Returning empty data.");
        }

        buckets.forEach(bucket -> {

            ParsedTopHits data = bucket.getAggregations().get("top_docs_by_feedDate");
            EngageNotificationDetails childDocuments = JSONUtils.fromJSON(data.getHits().getAt(0).getSourceAsString(), EngageNotificationDetails.class);
            childDocumentsList.add(childDocuments);
        });
        return childDocumentsList;
    }

    private List<EngageNotificationDetails> getChildCMDocData(List<String> pageIds, List<String> repliedCMFeedIds,String indexName) throws IOException {
        List<EngageNotificationDetails> childDocumentsList=new ArrayList<>();
        SearchRequest childSearchRequest = new SearchRequest(indexName);

        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();

        // Disable _source to avoid fetching document fields
        searchSourceBuilder.fetchSource(false);
        Script inline = new Script(ScriptType.INLINE, "painless", "doc['authorId.keyword'].value == doc['pageId.keyword'].value ? 1 : 0",
                new HashMap<>());

        // Create the script score function using ScriptScoreFunctionBuilder
        ScriptScoreFunctionBuilder scriptScoreFunctionBuilder = new ScriptScoreFunctionBuilder(inline);

        // Build the query part (bool query with script scoring)
        searchSourceBuilder.query(QueryBuilders.boolQuery()
                .must(QueryBuilders.scriptScoreQuery(
                        QueryBuilders.boolQuery()
                                .must(QueryBuilders.termsQuery(EVENT_PARENT_ID_ES.concat(KEYWORD), repliedCMFeedIds))
                                .must(QueryBuilders.termsQuery(PAGE_ID, pageIds)),
                        scriptScoreFunctionBuilder // pass the script score function builder here
                ))
        );

        // Sort by feedDate in ascending order
        searchSourceBuilder.sort(FEED_DATE, SortOrder.ASC);

        // Set the size to 0 to only return aggregations
        searchSourceBuilder.size(0);

        // Aggregations: Group by eventParentId and return top document per group by feedDate
        TermsAggregationBuilder aggregation = AggregationBuilders.terms("grouped_by_eventParentId")
                .field(EVENT_PARENT_ID_ES.concat(KEYWORD))
                .size(10000)
                .subAggregation(
                        AggregationBuilders.topHits("top_docs_by_feedDate")
                                .sort(FEED_DATE, SortOrder.ASC) // Sort by feedDate in ascending order
                                .size(1) // Limit to 1 document per group
                );
        searchSourceBuilder.aggregation(aggregation);

        childSearchRequest.source(searchSourceBuilder);

        // Execute the search query
        SearchResponse childSearchResponse = esService.search(childSearchRequest);


        //Get Child Document from aggregation
        if (childSearchResponse == null || childSearchResponse.getAggregations() == null) {
            LOGGER.error("SearchResponse or Aggregations is null. Returning empty data.");
        }


        ParsedTerms terms = childSearchResponse.getAggregations().get("grouped_by_eventParentId");
        List<? extends Terms.Bucket> buckets = terms.getBuckets();

        if (CollectionUtils.isEmpty(buckets)) {
            LOGGER.error("buckets is null. Returning empty data.");
        }

        buckets.forEach(bucket -> {

            ParsedTopHits data = bucket.getAggregations().get("top_docs_by_feedDate");
            EngageNotificationDetails childDocuments = JSONUtils.fromJSON(data.getHits().getAt(0).getSourceAsString(), EngageNotificationDetails.class);
            childDocumentsList.add(childDocuments);
        });
        return childDocumentsList;
    }

    private List<EngageNotificationDetails> getParentDocData(List<String> postFeedIds,Date startDate,Date endDate,String indexName,boolean isDM) {
        List<EngageNotificationDetails> parentDocList = new ArrayList<>();
        try {
            // Construct the bool query to get parent  documents
            BoolQueryBuilder postBoolQuery = QueryBuilders.boolQuery();

            // Query for parent documents (where isReplied = true)
            postBoolQuery.must(QueryBuilders.termQuery("isReplied", true))
                    .must(QueryBuilders.termsQuery(isDM ? "feedId.keyword" :"postId.keyword", postFeedIds))
                    .must(QueryBuilders.rangeQuery(FEED_DATE)
                            .gte(DateTimeUtils.convertDateToESDateTime(startDate))
                            .lte(DateTimeUtils.convertDateToESDateTime(endDate)));


            // Create a SearchRequest and specify the query
            SearchRequest searchRequest = new SearchRequest(indexName);
            searchRequest.source()
                    .query(postBoolQuery);

            // Execute the query
            SearchResponse parentSearchResponse = esService.search(searchRequest);
            for (SearchHit hit : parentSearchResponse.getHits().getHits()) {
                EngageNotificationDetails parentDocuments = JSONUtils.fromJSON(hit.getSourceAsString(), EngageNotificationDetails.class);
                parentDocList.add(parentDocuments);

            }
        }catch (Exception e){
            LOGGER.error("Error in executing ES query :{}",e.getMessage());
        }
       return parentDocList;
    }

    @Override
    public void handleDeleteEngageCase(EngageNotificationDetails feed) {
        if (!(SocialChannel.FACEBOOK.getName().equalsIgnoreCase(feed.getChannel()) ||
                SocialChannel.LINKEDIN.getName().equalsIgnoreCase(feed.getChannel()))) {
            return; // Only Facebook and LinkedIn support hard delete
        }
        try {
            if (!(COMMENT.name().equalsIgnoreCase(feed.getType()) || AD_COMMENT.name().equalsIgnoreCase(feed.getType()))) {
                return; // Only Comment or Ad-Comments can be hard deleted
            }
            // Case: To delete first brand reply on comment
            if (Boolean.TRUE.equals(feed.getIsBrandReply()) && Objects.nonNull(feed.getRepliedOnId())) {
                processDeleteBrandReply(feed);
            }
            feed.setIsDeletedFromChannel(true);
            feed.setIsDeleted(true);
            feed.setRepliedOnId(null);
            updateEngageDocInEsAndDb(feed);
        } catch (Exception e) {
            LOGGER.error("Error in marking engage feed as hard deleted: {}", e.getMessage());
        }
    }

    private void processDeleteBrandReply(EngageNotificationDetails feed) {
        EngageNotificationDetails parentFeed = Objects.nonNull(feed.getSubType())
                ? engageConverterService.fetchEsDocByFeedIdAndPageId(feed.getEventParentId(), feed.getPageId())
                : null;
        if (parentFeed == null) {
            LOGGER.error("Parent comment not found for feedId: {}", feed.getFeedId());
            return;
        }
        LOGGER.info("Parent Comment rawFeedId : {}" , parentFeed.getRawFeedId());

        EngageNotificationDetails nextBrandComment = engageConverterService
                .fetchNextBrandCommentByEventParentId(parentFeed.getFeedId(), feed.getPageId(), feed.getFeedDate());

        if (Objects.nonNull(nextBrandComment)) {
            LOGGER.info("Next brand reply rawFeedId : {}" , nextBrandComment.getRawFeedId());
            // Setting next brand reply as first brand reply
            updateParentFeedForFirstBrandReply(parentFeed, nextBrandComment.getFeedId(), nextBrandComment.getBirdeyeUserId(),
                    nextBrandComment.getFeedDate(), nextBrandComment.getLocationId());

            nextBrandComment.setRepliedOnId(feed.getRepliedOnId());
            updateEngageDocInEsAndDb(nextBrandComment);
        } else {
            LOGGER.info("No other brand reply found, so marking parent comment un-replied");
            // Marking parent comment as not replied
            parentFeed.setIsReplied(false);
            parentFeed.setBrandReplyId(null);
            parentFeed.setRepliedByUserId(null);
            parentFeed.setRepliedByUserName(null);
            parentFeed.setRelativeResponseTime(null);
            parentFeed.setAbsoluteResponseTime(null);
        }
        updateEngageDocInEsAndDb(parentFeed);
    }
    public void updateEngageDocInEsAndDb(EngageNotificationDetails feed){
        try {
            UpdateRequest request = new UpdateRequest(ElasticConstants.SOCIAL_ENGAGE_V2_FEED.getName(), feed.getRawFeedId().toString());
            String doc = JSONUtils.toJSONIncludingNull(feed); // Updating Null fields also
            esService.upsertDocument(doc, request);
            EngageFeedDetails engageFeedDetails = engageFeedDetailsRepo.findById(feed.getRawFeedId());
            if (Objects.nonNull(engageFeedDetails)) {
                engageFeedDetails.setMetaData(JSONUtils.toJSON(feed));
                engageFeedDetailsRepo.save(engageFeedDetails);
            }
        }catch (Exception e){
            LOGGER.error("Error in updating engage feed in ES and DB: {}", e.getMessage());
        }
    }

    public void updateParentFeedForFirstBrandReply(EngageNotificationDetails parentFeed, String brandReplyId, Integer birdEyeUserId, Date feedDate, Integer locationId){
        parentFeed.setBrandReplyId(brandReplyId);
        parentFeed.setRepliedByUserId(birdEyeUserId);
        parentFeed.setRepliedByUserName(businessCoreService.getFullUsername(birdEyeUserId));
        engageConverterService.updateResponseTime(parentFeed, feedDate, locationId);
    }
}
